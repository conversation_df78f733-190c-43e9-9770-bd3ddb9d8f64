#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turkish Phonetic Text Processor
Doğal Türkçe konuşma için fonetik tabanlı metin işleme
GitHub projelerinden ilham alınarak geliştirildi
"""

import re
import unicodedata
from typing import Dict, List, Optional, Tuple
from ..utils.logger import get_logger


class TurkishPhoneticProcessor:
    """Türkçe fonetik metin işleyici - doğal konuşma için"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Türkçe fonem haritası (Turkish-TTS projesinden ilham)
        self.turkish_phonemes = {
            'a': 'a', 'e': 'e', 'ı': '1', 'i': 'i', 'o': 'o', 'ö': '2', 'u': 'u', 'ü': 'y',
            'b': 'b', 'c': 'dZ', 'ç': 'tS', 'd': 'd', 'f': 'f', 'g': 'g', 'ğ': 'gj',
            'h': 'h', 'j': 'Z', 'k': 'k', 'l': 'l', 'm': 'm', 'n': 'n', 'p': 'p',
            'r': 'r', 's': 's', 'ş': 'S', 't': 't', 'v': 'v', 'y': 'j', 'z': 'z'
        }
        
        # Doğal kısaltma sözlüğü (bağlamsal)
        self.contextual_abbreviations = {
            # Akademik unvanlar
            r'\bDr\.\s+([A-ZÜĞŞÇÖI][a-züğşçöıi]+)': r'Doktor \1',
            r'\bProf\.\s+([A-ZÜĞŞÇÖI][a-züğşçöıi]+)': r'Profesör \1',
            r'\bDoç\.\s+([A-ZÜĞŞÇÖI][a-züğşçöıi]+)': r'Doçent \1',
            
            # Yaygın kısaltmalar (cümle başında)
            r'\bÖrn\.\s+': 'Örneğin ',
            r'\börn\.\s+': 'örneğin ',
            r'\bVs\.\s+': 've saire ',
            r'\bvs\.\s+': 've saire ',
            r'\bVb\.\s+': 've benzeri ',
            r'\bvb\.\s+': 've benzeri ',
            
            # Şirket isimleri
            r'\bLtd\.\s*Şti\.': 'Limited Şirketi',
            r'\bA\.Ş\.': 'Anonim Şirketi',
            r'\bInc\.': 'Incorporated',
            r'\bCorp\.': 'Corporation'
        }
        
        # Gelişmiş noktalama pattern'ları
        self.ordinal_pattern = re.compile(r'\b(\d+)\.\s+([A-ZÜĞŞÇÖI])')  # Sıralama: "1. madde"
        self.decimal_pattern = re.compile(r'\b(\d+)\.(\d+)\b')  # Ondalık: "1.5"
        self.web_dot_pattern = re.compile(r'(\w+)\.(\w+)')  # Web: "example.com"
        self.sentence_end_pattern = re.compile(r'([.!?])\s*$')  # Cümle sonu
        
        # Web adresi pattern'ları
        self.web_patterns = {
            r'\bwww\.': 'dabılyu dabılyu dabılyu nokta ',
            r'\.com\b': ' nokta kom',
            r'\.tr\b': ' nokta te er',
            r'\.org\b': ' nokta org',
            r'\.net\b': ' nokta net',
            r'\.edu\b': ' nokta edu',
            r'\.gov\b': ' nokta gov'
        }
        
        # Türkçe sayılar
        self.turkish_numbers = {
            '0': 'sıfır', '1': 'bir', '2': 'iki', '3': 'üç', '4': 'dört',
            '5': 'beş', '6': 'altı', '7': 'yedi', '8': 'sekiz', '9': 'dokuz',
            '10': 'on', '11': 'on bir', '12': 'on iki', '13': 'on üç', '14': 'on dört',
            '15': 'on beş', '16': 'on altı', '17': 'on yedi', '18': 'on sekiz', '19': 'on dokuz',
            '20': 'yirmi', '30': 'otuz', '40': 'kırk', '50': 'elli',
            '60': 'altmış', '70': 'yetmiş', '80': 'seksen', '90': 'doksan',
            '100': 'yüz', '1000': 'bin'
        }
        
        # Sıralama sayıları
        self.ordinal_numbers = {
            '1': 'birinci', '2': 'ikinci', '3': 'üçüncü', '4': 'dördüncü',
            '5': 'beşinci', '6': 'altıncı', '7': 'yedinci', '8': 'sekizinci',
            '9': 'dokuzuncu', '10': 'onuncu', '11': 'on birinci', '12': 'on ikinci'
        }
        
        # İngilizce kelime tespiti (sadece kesin İngilizce olanlar)
        self.english_words = {
            'example', 'hello', 'world', 'game', 'video', 'music',
            'resident', 'evil', 'requiem', 'call', 'duty', 'minecraft',
            'youtube', 'google', 'facebook', 'twitter', 'instagram',
            'windows', 'linux', 'android', 'iphone', 'samsung', 'apple',
            'february', 'january', 'march', 'april', 'may', 'june',
            'july', 'august', 'september', 'october', 'november', 'december'
        }

        # Türkçe'de de kullanılan kelimeler (işaretleme)
        self.ambiguous_words = {
            'test'  # Bu kelime hem Türkçe hem İngilizce olabilir
        }
    
    def process_text_naturally(self, text: str, language: str = "tr") -> str:
        """Metni doğal konuşma için işle"""
        try:
            if not text or not text.strip():
                return text
            
            # 1. Unicode normalizasyonu
            processed_text = unicodedata.normalize('NFKC', text)
            
            # 2. Bağlamsal kısaltmaları işle
            processed_text = self._process_contextual_abbreviations(processed_text)
            
            # 3. Sıralama sayılarını işle (sadece gerçek sıralama)
            processed_text = self._process_ordinal_numbers(processed_text)
            
            # 4. Web adreslerini işle
            processed_text = self._process_web_addresses(processed_text)
            
            # 5. Sayıları kelimeye çevir (bekleme kodları hariç)
            processed_text = self._convert_numbers_naturally(processed_text)
            
            # 6. İngilizce kelimeleri işaretle
            if language == "tr":
                processed_text = self._mark_english_words(processed_text)
            
            # 7. Noktalama temizliği
            processed_text = self._clean_punctuation(processed_text)
            
            # 8. Çoklu boşlukları temizle
            processed_text = re.sub(r'\s+', ' ', processed_text).strip()
            
            self.logger.debug(f"Doğal işleme: {len(text)} -> {len(processed_text)} karakter")
            return processed_text
            
        except Exception as e:
            self.logger.error(f"Doğal metin işleme hatası: {e}")
            return text
    
    def _process_contextual_abbreviations(self, text: str) -> str:
        """Bağlamsal kısaltmaları işle"""
        for pattern, replacement in self.contextual_abbreviations.items():
            text = re.sub(pattern, replacement, text)
        return text
    
    def _process_ordinal_numbers(self, text: str) -> str:
        """Sıralama sayılarını işle (sadece gerçek sıralama)"""
        def replace_ordinal(match):
            number = match.group(1)
            following_word = match.group(2)

            # Gerçek sıralama durumlarını kontrol et
            ordinal_indicators = ['madde', 'paragraf', 'bölüm', 'kısım', 'fasıl', 'sayfa', 'nokta', 'item']
            following_lower = following_word.lower()

            # Kelime başında sıralama kontrolü
            is_ordinal = any(following_lower.startswith(indicator) for indicator in ordinal_indicators)

            if is_ordinal:
                ordinal = self.ordinal_numbers.get(number, f"{number}uncu")
                return f"{ordinal} {following_word}"
            else:
                # Normal sayı olarak bırak
                return f"{number}. {following_word}"

        return self.ordinal_pattern.sub(replace_ordinal, text)
    
    def _process_web_addresses(self, text: str) -> str:
        """Web adreslerini doğal telaffuz için işle"""
        for pattern, replacement in self.web_patterns.items():
            text = re.sub(pattern, replacement, text)
        return text
    
    def _convert_numbers_naturally(self, text: str) -> str:
        """Sayıları doğal şekilde kelimeye çevir (bekleme kodları hariç)"""
        # Bekleme kodlarını koru
        pause_pattern = r'<#\d+(?:\.\d+)?#>'
        pauses = re.findall(pause_pattern, text)

        # Geçici placeholder'lar ile değiştir
        for i, pause in enumerate(pauses):
            text = text.replace(pause, f"__PAUSE_{i}__", 1)

        # Basit sayıları çevir (kelime sınırlarında, nokta ile bitmeyenler)
        for num, word in self.turkish_numbers.items():
            pattern = r'\b' + re.escape(num) + r'\b(?!\.)' # Nokta ile bitmeyenler
            text = re.sub(pattern, word, text)

        # Bekleme kodlarını geri koy
        for i, pause in enumerate(pauses):
            text = text.replace(f"__PAUSE_{i}__", pause, 1)

        return text
    
    def _mark_english_words(self, text: str) -> str:
        """İngilizce kelimeleri işaretle (XTTS için optimize edilmiş)"""
        # İngilizce kelime işaretleme sistemini devre dışı bırak
        # XTTS [EN][/EN] etiketlerini yanlış yorumluyor
        # Bunun yerine doğal telaffuz için kelimeyi olduğu gibi bırak
        return text
    
    def _clean_punctuation(self, text: str) -> str:
        """Gelişmiş noktalama işleme - XTTS için optimize"""

        # 1. Ondalık sayıları koru (1.5 gibi)
        decimal_placeholders = {}
        decimal_matches = self.decimal_pattern.findall(text)
        for i, (whole, decimal) in enumerate(decimal_matches):
            placeholder = f"__DECIMAL_{i}__"
            original = f"{whole}.{decimal}"
            decimal_placeholders[placeholder] = f"{whole} nokta {decimal}"
            text = text.replace(original, placeholder, 1)

        # 2. Web adreslerindeki noktalar zaten işlendi

        # 3. Sıralama sayıları zaten işlendi

        # 4. Cümle sonu noktalama - XTTS'nin yanlış yorumlamasını önle
        # Nokta sonrası gereksiz ek eklenmesini önlemek için
        text = re.sub(r'\.(\s|$)', r'. \1', text)  # Nokta sonrası boşluk garantile

        # 5. Çoklu noktalama temizle
        text = re.sub(r'\.{2,}', '...', text)  # İki veya daha fazla nokta -> üç nokta
        text = re.sub(r'!{2,}', '!', text)
        text = re.sub(r'\?{2,}', '?', text)

        # 6. Noktalama + boşluk + noktalama durumlarını temizle
        text = re.sub(r'([.!?])\s+([.!?])', r'\1', text)

        # 7. Ondalık sayıları geri koy
        for placeholder, replacement in decimal_placeholders.items():
            text = text.replace(placeholder, replacement)

        return text
    
    def convert_to_phonemes(self, text: str) -> str:
        """Metni fonem dizisine çevir (gelişmiş TTS için)"""
        phonemes = []
        
        for char in text.lower():
            if char in self.turkish_phonemes:
                phonemes.append(self.turkish_phonemes[char])
            elif char == ' ':
                phonemes.append(' ')
            elif char in '.,!?;:':
                phonemes.append(char)
        
        return ' '.join(phonemes)
    
    def estimate_speech_duration(self, text: str, wpm: int = 140) -> float:
        """Türkçe konuşma süresini tahmin et"""
        # Türkçe için optimize edilmiş kelime sayısı
        words = len(text.split())
        # Türkçe konuşma hızı genelde daha yavaş
        minutes = words / wpm
        return minutes * 60


# Global instance
_turkish_phonetic_processor = None

def get_turkish_phonetic_processor() -> TurkishPhoneticProcessor:
    """Global TurkishPhoneticProcessor instance'ını al"""
    global _turkish_phonetic_processor
    if _turkish_phonetic_processor is None:
        _turkish_phonetic_processor = TurkishPhoneticProcessor()
    return _turkish_phonetic_processor

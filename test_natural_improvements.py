#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Natural TTS Improvements Test
Doğal konuşma iyileştirmeleri testi
"""

import os
import time
from src.reclasttts.core.tts_engine import RecLastTTSEngine
from src.reclasttts.utils.turkish_phonetic_processor import get_turkish_phonetic_processor
from src.reclasttts.core.emotion_controller import get_emotion_controller


def test_natural_text_processing():
    """Doğal metin işleme testi"""
    print("\n📝 Doğal Metin İşleme Testi")
    print("=" * 50)
    
    processor = get_turkish_phonetic_processor()
    
    test_texts = [
        # Sıralama sayıları testi
        "1. madde çok önemli. 2. paragraf da önemli. Ancak 1.5 oranı normal sayı.",
        
        # Web adresleri testi  
        "Sitemiz www.example.com adresinde. test.tr sitesi de var.",
        
        # Kısaltmalar testi
        "Dr<PERSON>, Prof. <PERSON><PERSON><PERSON> vs. konuları konuştu. Örn. matematik.",
        
        # Karı<PERSON><PERSON>k dil testi
        "Resident Evil oyunu February 2026'da çıkacak. YouTube'da video var.",
        
        # Noktalama testi
        "Bu bir test... Gerçekten mi? Evet! Harika, değil mi?",
        
        # Türkçe karakter testi
        "Türkçe karakterler: ğüşıöç telaffuzu önemlidir."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Test {i}:")
        print(f"Orijinal: {text}")
        
        processed = processor.process_text_naturally(text, "tr")
        print(f"İşlenmiş: {processed}")
        
        # Değişiklikleri göster
        if text != processed:
            print("✅ Metin işlendi")
        else:
            print("ℹ️ Değişiklik yok")


def test_natural_emotions():
    """Doğal duygu kontrolü testi"""
    print("\n🎭 Doğal Duygu Kontrolü Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    emotion_controller = get_emotion_controller()
    
    # Duygu testleri
    emotion_tests = [
        {
            "text": "<scary>Bu korkunç bir hikaye...</scary> <#1#> <happy>Ama mutlu son var!</happy>",
            "emotion": "neutral",
            "description": "Karışık duygu testi"
        },
        {
            "text": "<whisper>Bu bir sır... Kimseye söyleme.</whisper>",
            "emotion": "neutral", 
            "description": "Fısıltı testi"
        },
        {
            "text": "<excited>Harika! Bu muhteşem bir haber!</excited>",
            "emotion": "neutral",
            "description": "Heyecan testi"
        },
        {
            "text": "Normal konuşma <#2#> uzun bekleme sonrası devam.",
            "emotion": "neutral",
            "description": "Bekleme testi"
        }
    ]
    
    for i, test in enumerate(emotion_tests, 1):
        print(f"\n📝 Test {i} - {test['description']}:")
        print(f"Metin: {test['text']}")
        
        # Duygusal işleme
        processed_text, segments = emotion_controller.process_emotional_text(
            test['text'], test['emotion'], "tr"
        )
        
        print(f"İşlenmiş: {processed_text}")
        print(f"Segment sayısı: {len(segments)}")
        
        for j, segment in enumerate(segments):
            print(f"  Segment {j+1}: {segment['emotion']} - {segment.get('text', 'N/A')[:50]}...")
        
        # TTS ile test
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion=test['emotion']
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/natural_emotion_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ TTS Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_problematic_cases():
    """Sorunlu durumlar testi"""
    print("\n🔧 Sorunlu Durumlar Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Tespit edilen sorunlu durumlar
    problem_tests = [
        {
            "text": "Bu bir sır.",
            "emotion": "whisper",
            "issue": "Nokta sonrası 'cı' ekleme sorunu"
        },
        {
            "text": "1. madde önemli.",
            "emotion": "neutral", 
            "issue": "Sıralama sayısı doğru işleme"
        },
        {
            "text": "www.example.com sitesi",
            "emotion": "neutral",
            "issue": "Web adresi telaffuzu (w -> dabılyu)"
        },
        {
            "text": "Türkçe karakterler: ğüşıöç",
            "emotion": "neutral",
            "issue": "Türkçe karakter telaffuzu"
        },
        {
            "text": "Test... nokta nokta nokta",
            "emotion": "neutral",
            "issue": "Üç nokta işleme"
        }
    ]
    
    for i, test in enumerate(problem_tests, 1):
        print(f"\n📝 Test {i} - {test['issue']}:")
        print(f"Metin: {test['text']}")
        print(f"Duygu: {test['emotion']}")
        
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion=test['emotion']
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/problem_test_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
                print(f"🎯 Kontrol edilecek: {test['issue']}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_comprehensive_scenario():
    """Kapsamlı senaryo testi"""
    print("\n🎬 Kapsamlı Doğal Konuşma Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Gerçek kullanım senaryosu - tüm iyileştirmeler
    scenario_text = """
    <excited>Merhaba arkadaşlar!</excited> Bugün RecLastTTS sisteminin doğal konuşma özelliklerini test ediyoruz.
    
    <#1#>
    
    Sistemin yeni özellikleri:
    1. Doğal Türkçe telaffuz
    2. İngilizce kelime tespiti: YouTube, Resident Evil gibi
    3. Web adresi telaffuzu: www.example.com
    4. Kısaltma işleme: Dr. Ahmet, Prof. Mehmet, vs.
    
    <#1.5#>
    
    <whisper>Gizli bilgi:</whisper> Sistem artık çok daha doğal konuşuyor.
    Türkçe karakterler ğüşıöç düzgün telaffuz ediliyor.
    
    <#0.5#>
    
    <scary>Dikkat!</scary> Test sırasında beklenmedik durumlar olabilir...
    <#1#>
    <happy>Ama endişelenmeyin, her şey kontrol altında!</happy>
    
    <calm>Test tamamlandı. Teşekkürler.</calm>
    """
    
    print("📝 Kapsamlı senaryo metni hazırlandı")
    print(f"📊 Metin uzunluğu: {len(scenario_text)} karakter")
    
    start_time = time.time()
    audio_data = engine.text_to_speech(
        text=scenario_text,
        language="tr",
        emotion="neutral",
        enable_chunking=True,
        chunk_size=75
    )
    end_time = time.time()
    
    if audio_data is not None:
        output_path = "output/natural_comprehensive.wav"
        os.makedirs("output", exist_ok=True)
        success = engine.save_audio(audio_data, output_path)
        
        if success:
            duration = len(audio_data) / 22050
            word_count = len(scenario_text.split())
            print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
            print(f"📁 Kaydedildi: {output_path}")
            print(f"📊 {word_count} kelime, {word_count / (end_time - start_time):.1f} kelime/saniye")
            print(f"🎯 Doğal konuşma kalitesini kontrol edin!")
        else:
            print("❌ Ses kaydedilemedi")
    else:
        print("❌ TTS işlemi başarısız")


def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS Natural Improvements Test")
    print("=" * 60)
    print("🎯 Doğal konuşma iyileştirmeleri test ediliyor...")
    
    try:
        # Testleri çalıştır
        test_natural_text_processing()
        test_natural_emotions()
        test_problematic_cases()
        test_comprehensive_scenario()
        
        print("\n🎉 Tüm testler tamamlandı!")
        print("📁 Ses dosyaları 'output/' klasöründe kaydedildi.")
        print("\n📋 Kontrol Edilecek İyileştirmeler:")
        print("✅ Sıralama sayıları (1. -> birinci)")
        print("✅ Web adresi telaffuzu (www -> dabılyu)")
        print("✅ Noktalama sonrası gereksiz ek önleme")
        print("✅ Duygu kontrolü iyileştirme")
        print("✅ Türkçe karakter telaffuzu")
        print("✅ İngilizce kelime tespiti")
        print("\n🎧 Ses dosyalarını dinleyerek kaliteyi değerlendirin!")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

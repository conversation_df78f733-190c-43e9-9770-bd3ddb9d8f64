#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turkish Prosody Engine
Türkçe için basit ve etkili prosodi kontrolü
"""

import re
from typing import Dict, List
from ..utils.logger import get_logger


class TurkishProsodyEngine:
    """Türkçe prosodi kontrolü - basit ve etkili yaklaşım"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def process_turkish_prosody(self, text: str) -> str:
        """Türkçe metni prosodi kurallarıyla işle"""
        try:
            processed_text = text
            
            # 1. Noktalama duraklamaları için boşluk ekle
            # Nokta sonrası duraklama
            processed_text = re.sub(r'\.(\s|$)', r'.   \1', processed_text)
            
            # Virgül sonrası kısa duraklama  
            processed_text = re.sub(r',(\s)', r',  \1', processed_text)
            
            # Soru işareti sonrası duraklama
            processed_text = re.sub(r'\?(\s|$)', r'?   \1', processed_text)
            
            # Ünlem sonrası duraklama
            processed_text = re.sub(r'!(\s|$)', r'!   \1', processed_text)
            
            # Üç nokta sonrası uzun duraklama
            processed_text = re.sub(r'\.{3,}(\s|$)', r'...     \1', processed_text)
            
            # 2. Bekleme kodlarını işle (<#1#> formatı)
            processed_text = re.sub(r'<#(\d+(?:\.\d+)?)#>', lambda m: '      ' * int(float(m.group(1))), processed_text)
            
            # 3. Ondalık sayıları Türkçe'ye çevir
            processed_text = re.sub(r'\b(\d+)\.(\d+)\b', self._convert_decimal, processed_text)
            
            # 4. Çoklu boşlukları temizle
            processed_text = re.sub(r'\s+', ' ', processed_text).strip()
            
            self.logger.info(f"Türkçe prosodi uygulandı: {len(text)} -> {len(processed_text)} karakter")
            return processed_text
            
        except Exception as e:
            self.logger.error(f"Prosodi işleme hatası: {e}")
            return text
    
    def _convert_decimal(self, match) -> str:
        """Ondalık sayıları Türkçe'ye çevir"""
        try:
            integer_part = match.group(1)
            decimal_part = match.group(2)
            
            # Basit çeviriler
            decimal_map = {
                '5': 'buçuk',
                '25': 'çeyrek',
                '75': 'üç çeyrek'
            }
            
            if decimal_part in decimal_map:
                return f"{integer_part} {decimal_map[decimal_part]}"
            else:
                return f"{integer_part} nokta {decimal_part}"
                
        except Exception:
            return match.group(0)
    
    def extract_prosody_features(self, text: str) -> Dict:
        """Prosodi özelliklerini çıkar"""
        features = {
            'sentence_count': len(re.findall(r'[.!?]+', text)),
            'comma_count': text.count(','),
            'question_count': text.count('?'),
            'exclamation_count': text.count('!'),
            'ellipsis_count': len(re.findall(r'\.{3,}', text)),
            'pause_codes': len(re.findall(r'<#\d+(?:\.\d+)?#>', text)),
            'decimal_numbers': len(re.findall(r'\b\d+\.\d+\b', text))
        }
        
        return features


# Global instance
_turkish_prosody_engine = None

def get_turkish_prosody_engine() -> TurkishProsodyEngine:
    """Global TurkishProsodyEngine instance'ını al"""
    global _turkish_prosody_engine
    if _turkish_prosody_engine is None:
        _turkish_prosody_engine = TurkishProsodyEngine()
    return _turkish_prosody_engine

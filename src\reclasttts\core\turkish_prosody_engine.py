#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Turkish Prosody Engine
Türkçe için gelişmiş prosodi ve ezgi kontrolü
Baran Uslu'nun araştırmasından ilham alınarak geliştirildi
"""

import re
import numpy as np
from typing import Dict, List, Optional, Tuple
from ..utils.logger import get_logger


class TurkishProsodyEngine:
    """Türkçe prosodi ve ezgi kontrolü - XTTS v2 için optimize"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Türkçe fiil çekim kural<PERSON> (Baran Uslu araştırmasından)
        self.verb_stress_patterns = {
            # O<PERSON>lu fiiller
            'positive_affirmative': {
                'pitch_mod': 0.0,      # Normal pitch
                'duration_mod': 1.0,   # Normal süre
                'energy_mod': 0.0,     # Normal enerji
                'stress_position': -2  # <PERSON><PERSON> ikinci hece
            },
            # <PERSON><PERSON><PERSON> fiiller  
            'negative_affirmative': {
                'pitch_mod': -0.1,     # Biraz alçak
                'duration_mod': 1.1,   # Biraz uzun
                'energy_mod': -0.05,   # Biraz düşük enerji
                'stress_position': -3  # Sondan üçüncü hece (değil)
            },
            # Olumlu soru
            'positive_interrogative': {
                'pitch_mod': 0.3,      # Yüksek pitch (soru tonu)
                'duration_mod': 1.2,   # Uzun süre
                'energy_mod': 0.2,     # Yüksek enerji
                'stress_position': -1  # Son hece (mi/mı)
            },
            # Olumsuz soru
            'negative_interrogative': {
                'pitch_mod': 0.25,     # Yüksek ama biraz düşük
                'duration_mod': 1.3,   # En uzun süre
                'energy_mod': 0.15,    # Orta-yüksek enerji
                'stress_position': -1  # Son hece
            }
        }
        
        # Noktalama prosodi kuralları
        self.punctuation_prosody = {
            '.': {
                'pitch_mod': -0.3,     # Düşen ton
                'duration_mod': 1.5,   # Uzun duraklama
                'energy_mod': -0.2,    # Düşen enerji
                'pause_duration': 0.8  # 800ms bekleme
            },
            ',': {
                'pitch_mod': 0.1,      # Hafif yükselen
                'duration_mod': 1.2,   # Kısa duraklama
                'energy_mod': 0.0,     # Normal enerji
                'pause_duration': 0.3  # 300ms bekleme
            },
            '!': {
                'pitch_mod': 0.4,      # Yüksek ton
                'duration_mod': 1.4,   # Uzun
                'energy_mod': 0.3,     # Yüksek enerji
                'pause_duration': 0.7  # 700ms bekleme
            },
            '?': {
                'pitch_mod': 0.5,      # En yüksek ton
                'duration_mod': 1.6,   # En uzun
                'energy_mod': 0.25,    # Yüksek enerji
                'pause_duration': 0.6  # 600ms bekleme
            },
            '...': {
                'pitch_mod': -0.2,     # Düşen ton
                'duration_mod': 2.0,   # Çok uzun
                'energy_mod': -0.3,    # Düşük enerji
                'pause_duration': 1.2  # 1200ms bekleme
            }
        }
        
        # Türkçe kelime vurgu kuralları
        self.word_stress_rules = {
            # Son hece vurgusu (genel kural)
            'default': -1,
            # İstisnalar
            'exceptions': {
                # Soru kelimeleri
                'nerede': 0, 'nasıl': 0, 'neden': 0, 'niçin': 0,
                # Zaman zarfları
                'bugün': -1, 'yarın': 0, 'dün': 0,
                # Yaygın kelimeler
                'şimdi': 0, 'sonra': 0, 'önce': 0
            }
        }
        
        # SSML template'leri
        self.ssml_templates = {
            'prosody': '<prosody rate="{rate}" pitch="{pitch}" volume="{volume}">{text}</prosody>',
            'break': '<break time="{time}s"/>',
            'emphasis': '<emphasis level="{level}">{text}</emphasis>',
            'phoneme': '<phoneme alphabet="ipa" ph="{phoneme}">{text}</phoneme>'
        }
    
    def process_turkish_prosody(self, text: str) -> str:
        """Türkçe metni prosodi kurallarıyla işle"""
        try:
            # 1. Cümle analizi
            sentences = self._split_sentences(text)
            processed_sentences = []
            
            for sentence in sentences:
                # 2. Fiil analizi ve prosodi
                sentence_with_verb_prosody = self._apply_verb_prosody(sentence)
                
                # 3. Kelime vurgusu
                sentence_with_stress = self._apply_word_stress(sentence_with_verb_prosody)
                
                # 4. Noktalama prosodisi
                sentence_with_punctuation = self._apply_punctuation_prosody(sentence_with_stress)
                
                processed_sentences.append(sentence_with_punctuation)
            
            result = ' '.join(processed_sentences)
            self.logger.info(f"Türkçe prosodi uygulandı: {len(text)} -> {len(result)} karakter")
            return result
            
        except Exception as e:
            self.logger.error(f"Prosodi işleme hatası: {e}")
            return text
    
    def _split_sentences(self, text: str) -> List[str]:
        """Metni cümlelere böl"""
        # Gelişmiş cümle bölme
        sentence_endings = r'[.!?]+(?:\s|$)'
        sentences = re.split(sentence_endings, text)
        return [s.strip() for s in sentences if s.strip()]
    
    def _apply_verb_prosody(self, sentence: str) -> str:
        """Fiil prosodisi uygula (Baran Uslu kuralları)"""
        try:
            # Türkçe fiil pattern'ları
            verb_patterns = {
                # Olumsuz fiiller (-mez, -maz, -mıyor, vs.)
                r'\b\w+m[aıeio]z\b': 'negative_affirmative',
                r'\b\w+m[ıi]yor\b': 'negative_affirmative', 
                r'\b\w+m[aıeio]d[ıi]\b': 'negative_affirmative',
                
                # Soru ekleri (mi, mı, mu, mü)
                r'\b\w+\s+m[ıiuü]\b': 'positive_interrogative',
                r'\b\w+m[aıeio]z\s+m[ıiuü]\b': 'negative_interrogative',
                
                # Olumlu fiiller (varsayılan)
                r'\b\w+[ıi]yor\b': 'positive_affirmative',
                r'\b\w+d[ıi]\b': 'positive_affirmative',
                r'\b\w+[aıeio]cak\b': 'positive_affirmative'
            }
            
            processed_sentence = sentence
            
            for pattern, verb_type in verb_patterns.items():
                matches = re.finditer(pattern, sentence, re.IGNORECASE)
                
                for match in matches:
                    verb = match.group()
                    prosody_params = self.verb_stress_patterns[verb_type]
                    
                    # SSML prosodi uygula
                    rate = f"{prosody_params['duration_mod']:.1f}"
                    pitch = f"{prosody_params['pitch_mod']:+.1f}st"  # semitone
                    volume = f"{prosody_params['energy_mod']:+.1f}dB"
                    
                    prosody_verb = self.ssml_templates['prosody'].format(
                        rate=rate, pitch=pitch, volume=volume, text=verb
                    )
                    
                    processed_sentence = processed_sentence.replace(verb, prosody_verb, 1)
            
            return processed_sentence
            
        except Exception as e:
            self.logger.error(f"Fiil prosodi hatası: {e}")
            return sentence
    
    def _apply_word_stress(self, sentence: str) -> str:
        """Kelime vurgusu uygula"""
        try:
            words = sentence.split()
            processed_words = []
            
            for word in words:
                # SSML etiketlerini koru
                if '<' in word and '>' in word:
                    processed_words.append(word)
                    continue
                
                # Noktalama temizle
                clean_word = re.sub(r'[^\w]', '', word.lower())
                
                # Vurgu kuralı belirle
                if clean_word in self.word_stress_rules['exceptions']:
                    stress_pos = self.word_stress_rules['exceptions'][clean_word]
                else:
                    stress_pos = self.word_stress_rules['default']
                
                # Vurgu uygula (sadece 2+ hece kelimeler için)
                if len(clean_word) > 3:  # Yaklaşık 2+ hece
                    emphasized_word = self.ssml_templates['emphasis'].format(
                        level="moderate", text=word
                    )
                    processed_words.append(emphasized_word)
                else:
                    processed_words.append(word)
            
            return ' '.join(processed_words)
            
        except Exception as e:
            self.logger.error(f"Kelime vurgu hatası: {e}")
            return sentence
    
    def _apply_punctuation_prosody(self, sentence: str) -> str:
        """Noktalama prosodisi uygula"""
        try:
            # Noktalama işaretlerini bul ve prosodi uygula
            for punct, prosody_params in self.punctuation_prosody.items():
                if punct in sentence:
                    # Bekleme süresi
                    pause_duration = prosody_params['pause_duration']
                    break_tag = self.ssml_templates['break'].format(time=pause_duration)
                    
                    # Noktalama öncesi prosodi
                    rate = f"{prosody_params['duration_mod']:.1f}"
                    pitch = f"{prosody_params['pitch_mod']:+.1f}st"
                    volume = f"{prosody_params['energy_mod']:+.1f}dB"
                    
                    # Noktalama öncesi kelimeye prosodi uygula
                    pattern = rf'(\w+)\s*\{re.escape(punct)}'
                    
                    def replace_with_prosody(match):
                        word = match.group(1)
                        prosody_word = self.ssml_templates['prosody'].format(
                            rate=rate, pitch=pitch, volume=volume, text=word
                        )
                        return f"{prosody_word}{punct}{break_tag}"
                    
                    sentence = re.sub(pattern, replace_with_prosody, sentence)
            
            return sentence
            
        except Exception as e:
            self.logger.error(f"Noktalama prosodi hatası: {e}")
            return sentence
    
    def generate_ssml(self, text: str, voice: str = "tr-TR-EmelNeural") -> str:
        """Tam SSML belgesi oluştur"""
        try:
            # Prosodi işle
            processed_text = self.process_turkish_prosody(text)
            
            # SSML belgesi oluştur
            ssml = f'''<?xml version="1.0" encoding="UTF-8"?>
<speak version="1.0" xmlns="http://www.w3.org/2001/10/synthesis" xml:lang="tr-TR">
    <voice name="{voice}">
        <prosody rate="medium" pitch="medium" volume="medium">
            {processed_text}
        </prosody>
    </voice>
</speak>'''
            
            return ssml
            
        except Exception as e:
            self.logger.error(f"SSML oluşturma hatası: {e}")
            return f'<speak>{text}</speak>'
    
    def extract_prosody_features(self, text: str) -> Dict:
        """Prosodi özelliklerini çıkar (analiz için)"""
        features = {
            'sentence_count': len(self._split_sentences(text)),
            'verb_patterns': {},
            'punctuation_count': {},
            'stress_words': 0
        }
        
        # Fiil pattern sayısı
        verb_patterns = {
            'negative': len(re.findall(r'\b\w+m[aıeio]z\b', text, re.IGNORECASE)),
            'interrogative': len(re.findall(r'\b\w+\s+m[ıiuü]\b', text, re.IGNORECASE)),
            'positive': len(re.findall(r'\b\w+[ıi]yor\b', text, re.IGNORECASE))
        }
        features['verb_patterns'] = verb_patterns
        
        # Noktalama sayısı
        for punct in self.punctuation_prosody.keys():
            features['punctuation_count'][punct] = text.count(punct)
        
        return features


# Global instance
_turkish_prosody_engine = None

def get_turkish_prosody_engine() -> TurkishProsodyEngine:
    """Global TurkishProsodyEngine instance'ını al"""
    global _turkish_prosody_engine
    if _turkish_prosody_engine is None:
        _turkish_prosody_engine = TurkishProsodyEngine()
    return _turkish_prosody_engine

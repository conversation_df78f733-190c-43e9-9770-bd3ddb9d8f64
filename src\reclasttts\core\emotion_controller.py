#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Natural Emotion Controller for TTS
Doğal duygu kontrolü - model tabanlı yaklaşım
"""

import re
import numpy as np
from typing import Dict, List, Optional, Tuple
from ..utils.logger import get_logger


class NaturalEmotionController:
    """Doğal duygu kontrolcüsü - XTTS v2 için optimize"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # <PERSON>y<PERSON> bazında doğal TTS parametreleri
        self.emotion_profiles = {
            "neutral": {
                "temperature": 0.7,
                "repetition_penalty": 5.0,
                "top_p": 0.8,
                "top_k": 50,
                "speed_modifier": 1.0,
                "pitch_modifier": 0.0,
                "energy_modifier": 0.0
            },
            "happy": {
                "temperature": 0.85,
                "repetition_penalty": 4.5,
                "top_p": 0.85,
                "top_k": 60,
                "speed_modifier": 1.1,
                "pitch_modifier": 0.1,
                "energy_modifier": 0.15
            },
            "sad": {
                "temperature": 0.6,
                "repetition_penalty": 6.0,
                "top_p": 0.75,
                "top_k": 40,
                "speed_modifier": 0.85,
                "pitch_modifier": -0.15,
                "energy_modifier": -0.2
            },
            "angry": {
                "temperature": 0.9,
                "repetition_penalty": 5.5,
                "top_p": 0.9,
                "top_k": 70,
                "speed_modifier": 1.2,
                "pitch_modifier": 0.2,
                "energy_modifier": 0.3
            },
            "excited": {
                "temperature": 0.88,
                "repetition_penalty": 4.0,
                "top_p": 0.88,
                "top_k": 65,
                "speed_modifier": 1.15,
                "pitch_modifier": 0.15,
                "energy_modifier": 0.25
            },
            "scary": {
                "temperature": 0.5,
                "repetition_penalty": 7.0,
                "top_p": 0.7,
                "top_k": 30,
                "speed_modifier": 0.8,
                "pitch_modifier": -0.25,
                "energy_modifier": -0.1
            },
            "whisper": {
                "temperature": 0.3,  # Çok düşük - daha stabil fısıltı
                "repetition_penalty": 10.0,  # Yüksek - tekrar önle
                "top_p": 0.5,  # Çok düşük - sınırlı kelime seçimi
                "top_k": 15,  # Çok düşük - fısıltı için
                "speed_modifier": 0.6,  # Yavaş konuşma
                "pitch_modifier": -0.3,  # Alçak ton
                "energy_modifier": -0.6  # Çok düşük enerji
            },
            "shout": {
                "temperature": 1.0,
                "repetition_penalty": 3.5,
                "top_p": 0.95,
                "top_k": 80,
                "speed_modifier": 1.3,
                "pitch_modifier": 0.3,
                "energy_modifier": 0.5
            },
            "calm": {
                "temperature": 0.55,
                "repetition_penalty": 6.5,
                "top_p": 0.72,
                "top_k": 35,
                "speed_modifier": 0.9,
                "pitch_modifier": -0.05,
                "energy_modifier": -0.1
            }
        }
        
        # Duygu geçiş pattern'ları
        self.emotion_patterns = {
            r'<happy>(.*?)</happy>': ('happy', r'\1'),
            r'<sad>(.*?)</sad>': ('sad', r'\1'),
            r'<angry>(.*?)</angry>': ('angry', r'\1'),
            r'<excited>(.*?)</excited>': ('excited', r'\1'),
            r'<scary>(.*?)</scary>': ('scary', r'\1'),
            r'<whisper>(.*?)</whisper>': ('whisper', r'\1'),
            r'<shout>(.*?)</shout>': ('shout', r'\1'),
            r'<calm>(.*?)</calm>': ('calm', r'\1'),
            r'<laugh>': ('laugh_effect', ''),
        }
        
        # Bekleme pattern'ları (gelişmiş)
        self.pause_pattern = re.compile(r'<#(\d+(?:\.\d+)?)#>')

        # XTTS için bekleme işleme (sessizlik)
        self.pause_replacements = {
            '<#0.5#>': '   ',  # Kısa sessizlik
            '<#1#>': '     ',  # Orta sessizlik
            '<#1.5#>': '       ',  # Uzun sessizlik
            '<#2#>': '         ',  # Çok uzun sessizlik
            '<#3#>': '           '  # Çok çok uzun sessizlik
        }
        
        # Türkçe için dil özel ayarları
        self.turkish_adjustments = {
            "base_repetition_penalty": 1.5,  # Türkçe tekrarları önlemek için
            "base_temperature_reduction": 0.1,  # Daha stabil Türkçe için
            "consonant_clarity": 0.1  # Türkçe ünsüzler için
        }
    
    def process_emotional_text(self, text: str, base_emotion: str = "neutral", language: str = "tr") -> Tuple[str, List[Dict]]:
        """
        Duygusal metni işle ve segment'lere böl
        
        Returns:
            Tuple[işlenmiş_metin, duygu_segmentleri]
        """
        try:
            segments = []
            processed_text = text
            current_position = 0
            
            # Duygu etiketlerini bul ve işle
            for pattern, (emotion, replacement) in self.emotion_patterns.items():
                matches = list(re.finditer(pattern, processed_text))
                
                for match in matches:
                    start, end = match.span()
                    
                    # Segment bilgisi
                    segment = {
                        "start_pos": start,
                        "end_pos": end,
                        "emotion": emotion,
                        "text": match.group(1) if match.groups() else "",
                        "parameters": self.get_emotion_parameters(emotion, language)
                    }
                    segments.append(segment)
                    
                    # Metni temizle
                    if replacement:
                        processed_text = re.sub(pattern, replacement, processed_text, count=1)
                    else:
                        processed_text = re.sub(pattern, '', processed_text, count=1)
            
            # Bekleme kodlarını işle
            processed_text, pause_segments = self._process_pause_codes(processed_text)
            segments.extend(pause_segments)
            
            # Segment'leri pozisyona göre sırala
            segments.sort(key=lambda x: x["start_pos"])
            
            # Eğer hiç duygu segmenti yoksa, tüm metin için base emotion kullan
            if not segments:
                segments = [{
                    "start_pos": 0,
                    "end_pos": len(processed_text),
                    "emotion": base_emotion,
                    "text": processed_text,
                    "parameters": self.get_emotion_parameters(base_emotion, language)
                }]
            
            self.logger.debug(f"Duygusal işleme: {len(segments)} segment oluşturuldu")
            return processed_text, segments
            
        except Exception as e:
            self.logger.error(f"Duygusal metin işleme hatası: {e}")
            # Hata durumunda basit segment döndür
            return text, [{
                "start_pos": 0,
                "end_pos": len(text),
                "emotion": base_emotion,
                "text": text,
                "parameters": self.get_emotion_parameters(base_emotion, language)
            }]
    
    def get_emotion_parameters(self, emotion: str, language: str = "tr") -> Dict:
        """Duygu için TTS parametrelerini al"""
        if emotion not in self.emotion_profiles:
            emotion = "neutral"
        
        params = self.emotion_profiles[emotion].copy()
        
        # Türkçe için özel ayarlamalar
        if language == "tr":
            params["repetition_penalty"] += self.turkish_adjustments["base_repetition_penalty"]
            params["temperature"] -= self.turkish_adjustments["base_temperature_reduction"]
            
            # Minimum değerleri koru
            params["temperature"] = max(0.3, params["temperature"])
            params["repetition_penalty"] = min(10.0, params["repetition_penalty"])
        
        return params
    
    def _process_pause_codes(self, text: str) -> Tuple[str, List[Dict]]:
        """Bekleme kodlarını işle (XTTS için optimize)"""
        pause_segments = []

        # Önce bilinen bekleme kodlarını değiştir
        processed_text = text
        for pause_code, replacement in self.pause_replacements.items():
            if pause_code in processed_text:
                processed_text = processed_text.replace(pause_code, replacement)

                # Segment bilgisi
                segment = {
                    "start_pos": 0,  # Basitleştirilmiş
                    "end_pos": len(pause_code),
                    "emotion": "pause",
                    "text": replacement,
                    "duration": float(pause_code.replace('<#', '').replace('#>', '')),
                    "parameters": {"pause_duration": float(pause_code.replace('<#', '').replace('#>', ''))}
                }
                pause_segments.append(segment)

        # Diğer bekleme kodları için genel pattern
        def replace_pause(match):
            duration = float(match.group(1))

            # Bekleme segmenti
            segment = {
                "start_pos": match.start(),
                "end_pos": match.end(),
                "emotion": "pause",
                "text": "",
                "duration": duration,
                "parameters": {"pause_duration": duration}
            }
            pause_segments.append(segment)

            # Sessizlik için boşluk döndür (XTTS'nin yanlış okumasını önle)
            return "   "  # Üç boşluk - daha uzun duraklama

        processed_text = self.pause_pattern.sub(replace_pause, processed_text)
        return processed_text, pause_segments
    
    def apply_emotion_to_audio(self, audio_data: np.ndarray, emotion: str, sample_rate: int = 22050) -> np.ndarray:
        """
        Ses verisine duygu efekti uygula (post-processing)
        Bu fonksiyon gelecekte ses işleme kütüphaneleri ile genişletilebilir
        """
        try:
            if emotion == "neutral" or audio_data is None:
                return audio_data
            
            # Duygu profili al
            profile = self.emotion_profiles.get(emotion, self.emotion_profiles["neutral"])
            
            # Basit ses işleme efektleri
            processed_audio = audio_data.copy()
            
            # Hız değişikliği (basit resampling)
            speed_mod = profile.get("speed_modifier", 1.0)
            if speed_mod != 1.0:
                # Basit hız değişikliği - gelecekte daha gelişmiş yöntemler kullanılabilir
                new_length = int(len(processed_audio) / speed_mod)
                if new_length > 0:
                    processed_audio = np.interp(
                        np.linspace(0, len(processed_audio) - 1, new_length),
                        np.arange(len(processed_audio)),
                        processed_audio
                    )
            
            # Enerji değişikliği (volume)
            energy_mod = profile.get("energy_modifier", 0.0)
            if energy_mod != 0.0:
                volume_factor = 1.0 + energy_mod
                processed_audio = processed_audio * volume_factor
                # Clipping önle
                processed_audio = np.clip(processed_audio, -1.0, 1.0)
            
            return processed_audio
            
        except Exception as e:
            self.logger.error(f"Ses duygu işleme hatası: {e}")
            return audio_data
    
    def get_supported_emotions(self) -> List[str]:
        """Desteklenen duyguları al"""
        return list(self.emotion_profiles.keys())
    
    def validate_emotion(self, emotion: str) -> str:
        """Duygu geçerliliğini kontrol et"""
        if emotion in self.emotion_profiles:
            return emotion
        else:
            self.logger.warning(f"Desteklenmeyen duygu: {emotion}, 'neutral' kullanılıyor")
            return "neutral"


# Global instance
_emotion_controller = None

def get_emotion_controller() -> NaturalEmotionController:
    """Global NaturalEmotionController instance'ını al"""
    global _emotion_controller
    if _emotion_controller is None:
        _emotion_controller = NaturalEmotionController()
    return _emotion_controller

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XTTS Prosody Controller
XTTS v2 için basit parametrik kontrol
"""

import numpy as np
from typing import Dict
from ..utils.logger import get_logger


class XTTSProsodyController:
    """XTTS v2 için basit prosodi kontrolü"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Türkçe için optimize edilmiş base değerler
        self.turkish_base = {
            'temperature': 0.65,
            'repetition_penalty': 8.0,
            'top_p': 0.75,
            'top_k': 45
        }
    
    def apply_prosody_to_generation(self, 
                                   text: str, 
                                   prosody_params: Dict,
                                   base_params: Dict) -> Dict:
        """Prosodi parametrelerini XTTS generation parametrelerine uygula"""
        try:
            # Base parametreleri kopyala
            modified_params = base_params.copy()
            
            # Prosodi modifikasyonları uygula
            if 'pitch_mod' in prosody_params:
                modified_params = self._apply_pitch_modification(
                    modified_params, prosody_params['pitch_mod']
                )
            
            if 'duration_mod' in prosody_params:
                modified_params = self._apply_duration_modification(
                    modified_params, prosody_params['duration_mod']
                )
            
            if 'energy_mod' in prosody_params:
                modified_params = self._apply_energy_modification(
                    modified_params, prosody_params['energy_mod']
                )
            
            # Türkçe optimizasyonları uygula
            modified_params = self._apply_turkish_optimizations(modified_params)
            
            # Parametre sınırlarını kontrol et
            modified_params = self._clamp_parameters(modified_params)
            
            self.logger.debug(f"Prosodi parametreleri uygulandı: {prosody_params}")
            return modified_params
            
        except Exception as e:
            self.logger.error(f"Prosodi uygulama hatası: {e}")
            return base_params
    
    def _apply_pitch_modification(self, params: Dict, pitch_mod: float) -> Dict:
        """Pitch modifikasyonu uygula"""
        # Pitch modifikasyonu için temperature ve top_p ayarla
        if pitch_mod > 0:  # Yüksek pitch
            params['temperature'] = min(params.get('temperature', 0.7) + pitch_mod * 0.3, 1.5)
            params['top_p'] = min(params.get('top_p', 0.8) + pitch_mod * 0.1, 1.0)
        else:  # Düşük pitch
            params['temperature'] = max(params.get('temperature', 0.7) + pitch_mod * 0.2, 0.1)
            params['top_p'] = max(params.get('top_p', 0.8) + pitch_mod * 0.1, 0.1)
        
        return params
    
    def _apply_duration_modification(self, params: Dict, duration_mod: float) -> Dict:
        """Duration modifikasyonu uygula"""
        # Duration için repetition_penalty ayarla
        if duration_mod > 1.0:  # Uzun konuşma
            params['repetition_penalty'] = max(
                params.get('repetition_penalty', 5.0) - (duration_mod - 1.0) * 2.0, 1.0
            )
        else:  # Kısa konuşma
            params['repetition_penalty'] = min(
                params.get('repetition_penalty', 5.0) + (1.0 - duration_mod) * 3.0, 15.0
            )
        
        return params
    
    def _apply_energy_modification(self, params: Dict, energy_mod: float) -> Dict:
        """Energy modifikasyonu uygula"""
        # Energy için top_k ve temperature ayarla
        if energy_mod > 0:  # Yüksek enerji
            params['top_k'] = min(params.get('top_k', 50) + int(energy_mod * 30), 100)
            params['temperature'] = min(params.get('temperature', 0.7) + energy_mod * 0.2, 1.5)
        else:  # Düşük enerji
            params['top_k'] = max(params.get('top_k', 50) + int(energy_mod * 20), 1)
            params['temperature'] = max(params.get('temperature', 0.7) + energy_mod * 0.15, 0.1)
        
        return params
    
    def _apply_turkish_optimizations(self, params: Dict) -> Dict:
        """Türkçe için özel optimizasyonlar"""
        # Türkçe base değerlerini uygula
        params['temperature'] = params.get('temperature', self.turkish_base['temperature'])
        params['repetition_penalty'] = max(
            params.get('repetition_penalty', self.turkish_base['repetition_penalty']),
            self.turkish_base['repetition_penalty']
        )
        params['top_p'] = params.get('top_p', self.turkish_base['top_p'])
        params['top_k'] = params.get('top_k', self.turkish_base['top_k'])
        
        return params
    
    def _clamp_parameters(self, params: Dict) -> Dict:
        """Parametreleri güvenli aralıklarda tut"""
        if 'temperature' in params:
            params['temperature'] = np.clip(params['temperature'], 0.1, 1.5)
        
        if 'repetition_penalty' in params:
            params['repetition_penalty'] = np.clip(params['repetition_penalty'], 1.0, 15.0)
        
        if 'top_p' in params:
            params['top_p'] = np.clip(params['top_p'], 0.1, 1.0)
        
        if 'top_k' in params:
            params['top_k'] = int(np.clip(params['top_k'], 1, 100))
        
        return params


# Global instance
_xtts_prosody_controller = None

def get_xtts_prosody_controller() -> XTTSProsodyController:
    """Global XTTSProsodyController instance'ını al"""
    global _xtts_prosody_controller
    if _xtts_prosody_controller is None:
        _xtts_prosody_controller = XTTSProsodyController()
    return _xtts_prosody_controller

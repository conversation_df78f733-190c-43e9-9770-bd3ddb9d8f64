#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XTTS Prosody Controller
XTTS v2 için gelişmiş prosodi kontrolü
"""

import numpy as np
import torch
import librosa
from typing import Dict, List, Optional, Tuple, Any
from ..utils.logger import get_logger


class XTTSProsodyController:
    """XTTS v2 için gelişmiş prosodi kontrolü"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # XTTS v2 için optimize edilmiş parametreler
        self.prosody_params = {
            'temperature_range': (0.1, 1.5),      # Çeşitlilik kontrolü
            'repetition_penalty_range': (1.0, 15.0),  # Tekrar önleme
            'top_p_range': (0.1, 1.0),            # Nucleus sampling
            'top_k_range': (1, 100),              # Top-k sampling
            'length_penalty_range': (0.5, 2.0),   # Uzunluk kontrolü
            'speed_range': (0.5, 2.0),            # <PERSON>ız kontrolü
        }
        
        # Türkçe için özel ayarlar
        self.turkish_optimizations = {
            'base_temperature': 0.65,              # Türkçe için optimal
            'base_repetition_penalty': 8.0,       # Türkçe tekrar önleme
            'base_top_p': 0.75,                   # Türkçe için optimal
            'base_top_k': 45,                     # Türkçe için optimal
            'consonant_clarity_boost': 0.15,      # Ünsüz netliği
            'vowel_harmony_weight': 0.2           # Ünlü uyumu
        }
        
        # Prosodi modifikasyon fonksiyonları
        self.prosody_modifiers = {
            'pitch': self._modify_pitch,
            'duration': self._modify_duration,
            'energy': self._modify_energy,
            'pause': self._insert_pause
        }
    
    def apply_prosody_to_generation(self, 
                                   text: str, 
                                   prosody_params: Dict,
                                   base_params: Dict) -> Dict:
        """Prosodi parametrelerini XTTS generation parametrelerine uygula"""
        try:
            # Base parametreleri kopyala
            modified_params = base_params.copy()
            
            # Prosodi modifikasyonları uygula
            if 'pitch_mod' in prosody_params:
                modified_params = self._apply_pitch_modification(
                    modified_params, prosody_params['pitch_mod']
                )
            
            if 'duration_mod' in prosody_params:
                modified_params = self._apply_duration_modification(
                    modified_params, prosody_params['duration_mod']
                )
            
            if 'energy_mod' in prosody_params:
                modified_params = self._apply_energy_modification(
                    modified_params, prosody_params['energy_mod']
                )
            
            # Türkçe optimizasyonları uygula
            modified_params = self._apply_turkish_optimizations(modified_params)
            
            # Parametre sınırlarını kontrol et
            modified_params = self._clamp_parameters(modified_params)
            
            self.logger.debug(f"Prosodi parametreleri uygulandı: {prosody_params}")
            return modified_params
            
        except Exception as e:
            self.logger.error(f"Prosodi uygulama hatası: {e}")
            return base_params
    
    def _apply_pitch_modification(self, params: Dict, pitch_mod: float) -> Dict:
        """Pitch modifikasyonu uygula"""
        # Pitch modifikasyonu için temperature ve top_p ayarla
        if pitch_mod > 0:  # Yüksek pitch
            params['temperature'] = min(params.get('temperature', 0.7) + pitch_mod * 0.3, 1.5)
            params['top_p'] = min(params.get('top_p', 0.8) + pitch_mod * 0.1, 1.0)
        else:  # Düşük pitch
            params['temperature'] = max(params.get('temperature', 0.7) + pitch_mod * 0.2, 0.1)
            params['top_p'] = max(params.get('top_p', 0.8) + pitch_mod * 0.1, 0.1)
        
        return params
    
    def _apply_duration_modification(self, params: Dict, duration_mod: float) -> Dict:
        """Duration modifikasyonu uygula"""
        # Duration için repetition_penalty ve length_penalty ayarla
        if duration_mod > 1.0:  # Uzun konuşma
            params['repetition_penalty'] = max(
                params.get('repetition_penalty', 5.0) - (duration_mod - 1.0) * 2.0, 1.0
            )
        else:  # Kısa konuşma
            params['repetition_penalty'] = min(
                params.get('repetition_penalty', 5.0) + (1.0 - duration_mod) * 3.0, 15.0
            )
        
        return params
    
    def _apply_energy_modification(self, params: Dict, energy_mod: float) -> Dict:
        """Energy modifikasyonu uygula"""
        # Energy için top_k ve temperature ayarla
        if energy_mod > 0:  # Yüksek enerji
            params['top_k'] = min(params.get('top_k', 50) + int(energy_mod * 30), 100)
            params['temperature'] = min(params.get('temperature', 0.7) + energy_mod * 0.2, 1.5)
        else:  # Düşük enerji
            params['top_k'] = max(params.get('top_k', 50) + int(energy_mod * 20), 1)
            params['temperature'] = max(params.get('temperature', 0.7) + energy_mod * 0.15, 0.1)
        
        return params
    
    def _apply_turkish_optimizations(self, params: Dict) -> Dict:
        """Türkçe için özel optimizasyonlar"""
        # Türkçe base değerlerini uygula
        params['temperature'] = params.get('temperature', self.turkish_optimizations['base_temperature'])
        params['repetition_penalty'] = max(
            params.get('repetition_penalty', self.turkish_optimizations['base_repetition_penalty']),
            self.turkish_optimizations['base_repetition_penalty']
        )
        params['top_p'] = params.get('top_p', self.turkish_optimizations['base_top_p'])
        params['top_k'] = params.get('top_k', self.turkish_optimizations['base_top_k'])
        
        return params
    
    def _clamp_parameters(self, params: Dict) -> Dict:
        """Parametreleri güvenli aralıklarda tut"""
        if 'temperature' in params:
            params['temperature'] = np.clip(
                params['temperature'], 
                self.prosody_params['temperature_range'][0],
                self.prosody_params['temperature_range'][1]
            )
        
        if 'repetition_penalty' in params:
            params['repetition_penalty'] = np.clip(
                params['repetition_penalty'],
                self.prosody_params['repetition_penalty_range'][0],
                self.prosody_params['repetition_penalty_range'][1]
            )
        
        if 'top_p' in params:
            params['top_p'] = np.clip(
                params['top_p'],
                self.prosody_params['top_p_range'][0],
                self.prosody_params['top_p_range'][1]
            )
        
        if 'top_k' in params:
            params['top_k'] = int(np.clip(
                params['top_k'],
                self.prosody_params['top_k_range'][0],
                self.prosody_params['top_k_range'][1]
            ))
        
        return params
    
    def post_process_audio(self, 
                          audio: np.ndarray, 
                          prosody_params: Dict,
                          sample_rate: int = 22050) -> np.ndarray:
        """Ses çıktısına prosodi post-processing uygula"""
        try:
            processed_audio = audio.copy()
            
            # Pitch modifikasyonu
            if 'pitch_mod' in prosody_params and prosody_params['pitch_mod'] != 0:
                processed_audio = self._modify_pitch(
                    processed_audio, prosody_params['pitch_mod'], sample_rate
                )
            
            # Duration modifikasyonu
            if 'duration_mod' in prosody_params and prosody_params['duration_mod'] != 1.0:
                processed_audio = self._modify_duration(
                    processed_audio, prosody_params['duration_mod'], sample_rate
                )
            
            # Energy modifikasyonu
            if 'energy_mod' in prosody_params and prosody_params['energy_mod'] != 0:
                processed_audio = self._modify_energy(
                    processed_audio, prosody_params['energy_mod']
                )
            
            return processed_audio
            
        except Exception as e:
            self.logger.error(f"Audio post-processing hatası: {e}")
            return audio
    
    def _modify_pitch(self, audio: np.ndarray, pitch_mod: float, sample_rate: int) -> np.ndarray:
        """Pitch modifikasyonu (basit implementasyon)"""
        try:
            # Pitch shifting için librosa kullan
            # pitch_mod: -1.0 to 1.0 range, semitone cinsinden
            n_steps = pitch_mod * 12  # semitone to steps
            
            if abs(n_steps) > 0.1:  # Sadece anlamlı değişiklikler için
                shifted_audio = librosa.effects.pitch_shift(
                    audio, sr=sample_rate, n_steps=n_steps
                )
                return shifted_audio
            
            return audio
            
        except Exception as e:
            self.logger.error(f"Pitch modifikasyon hatası: {e}")
            return audio
    
    def _modify_duration(self, audio: np.ndarray, duration_mod: float, sample_rate: int) -> np.ndarray:
        """Duration modifikasyonu (time stretching)"""
        try:
            if abs(duration_mod - 1.0) > 0.05:  # Sadece anlamlı değişiklikler için
                stretched_audio = librosa.effects.time_stretch(audio, rate=1.0/duration_mod)
                return stretched_audio
            
            return audio
            
        except Exception as e:
            self.logger.error(f"Duration modifikasyon hatası: {e}")
            return audio
    
    def _modify_energy(self, audio: np.ndarray, energy_mod: float) -> np.ndarray:
        """Energy modifikasyonu (volume)"""
        try:
            # Energy modifikasyonu basit volume kontrolü
            volume_factor = 1.0 + energy_mod
            volume_factor = np.clip(volume_factor, 0.1, 3.0)  # Güvenli aralık
            
            modified_audio = audio * volume_factor
            
            # Clipping önle
            modified_audio = np.clip(modified_audio, -1.0, 1.0)
            
            return modified_audio
            
        except Exception as e:
            self.logger.error(f"Energy modifikasyon hatası: {e}")
            return audio
    
    def _insert_pause(self, audio: np.ndarray, pause_duration: float, sample_rate: int) -> np.ndarray:
        """Ses içine bekleme ekle"""
        try:
            pause_samples = int(pause_duration * sample_rate)
            silence = np.zeros(pause_samples, dtype=audio.dtype)
            
            # Ses sonuna sessizlik ekle
            paused_audio = np.concatenate([audio, silence])
            
            return paused_audio
            
        except Exception as e:
            self.logger.error(f"Pause ekleme hatası: {e}")
            return audio
    
    def analyze_prosody_quality(self, 
                               original_audio: np.ndarray,
                               processed_audio: np.ndarray,
                               sample_rate: int = 22050) -> Dict:
        """Prosodi kalitesini analiz et"""
        try:
            analysis = {}
            
            # Temel istatistikler
            analysis['duration_change'] = len(processed_audio) / len(original_audio)
            analysis['energy_change'] = np.mean(processed_audio**2) / np.mean(original_audio**2)
            
            # Spektral analiz
            orig_stft = librosa.stft(original_audio)
            proc_stft = librosa.stft(processed_audio)
            
            analysis['spectral_similarity'] = np.corrcoef(
                np.abs(orig_stft).flatten(),
                np.abs(proc_stft).flatten()
            )[0, 1]
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Prosodi analiz hatası: {e}")
            return {}


# Global instance
_xtts_prosody_controller = None

def get_xtts_prosody_controller() -> XTTSProsodyController:
    """Global XTTSProsodyController instance'ını al"""
    global _xtts_prosody_controller
    if _xtts_prosody_controller is None:
        _xtts_prosody_controller = XTTSProsodyController()
    return _xtts_prosody_controller

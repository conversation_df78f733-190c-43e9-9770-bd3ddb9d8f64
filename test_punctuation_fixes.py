#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Punctuation Fixes Test
Noktalama düzeltmeleri testi
"""

import os
import time
from src.reclasttts.core.tts_engine import RecLastTTSEngine
from src.reclasttts.utils.turkish_phonetic_processor import get_turkish_phonetic_processor
from src.reclasttts.core.emotion_controller import get_emotion_controller


def test_punctuation_fixes():
    """Noktalama düzeltmeleri testi"""
    print("\n📝 Noktalama Düzeltmeleri Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    processor = get_turkish_phonetic_processor()
    
    # Noktalama sorunları testleri
    punctuation_tests = [
        {
            "text": "Bu bir sır.",
            "issue": "Nokta sonrası 'cı' ekleme sorunu",
            "expected": "Nokta sonrası ek yok"
        },
        {
            "text": "Test tamamlandı. Teşekkürler.",
            "issue": "Cümle sonu nokta sonrası 'di' ekleme",
            "expected": "Normal cümle sonu duraklaması"
        },
        {
            "text": "Bu bir test... Üç nokta testi.",
            "issue": "Üç nokta işleme",
            "expected": "Üç nokta normal duraklama"
        },
        {
            "text": "1. madde önemli. 2. paragraf da var.",
            "issue": "Sıralama vs cümle sonu nokta",
            "expected": "1. -> birinci, cümle sonu normal"
        },
        {
            "text": "Sayı 1.5 oranında. Web www.example.com sitesi.",
            "issue": "Ondalık vs web vs cümle nokta",
            "expected": "1.5 -> bir nokta beş, www -> dabılyu"
        },
        {
            "text": "Dr. Ahmet geldi. Prof. Mehmet de var.",
            "issue": "Kısaltma vs cümle sonu nokta",
            "expected": "Dr. -> Doktor, cümle sonu normal"
        }
    ]
    
    for i, test in enumerate(punctuation_tests, 1):
        print(f"\n📝 Test {i} - {test['issue']}:")
        print(f"Orijinal: {test['text']}")
        print(f"Beklenen: {test['expected']}")
        
        # Metin işleme testi
        processed = processor.process_text_naturally(test['text'], "tr")
        print(f"İşlenmiş: {processed}")
        
        # TTS testi
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion="neutral"
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/punctuation_fix_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ TTS Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
                print(f"🎯 Kontrol: {test['issue']}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_english_word_fixes():
    """İngilizce kelime düzeltmeleri testi"""
    print("\n🌍 İngilizce Kelime Düzeltmeleri Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    processor = get_turkish_phonetic_processor()
    
    # İngilizce kelime testleri
    english_tests = [
        {
            "text": "YouTube videosunu izledim.",
            "issue": "İngilizce kelime işaretleme sorunu",
            "expected": "YouTube normal telaffuz (EN EN yok)"
        },
        {
            "text": "Resident Evil oyunu güzel.",
            "issue": "İki kelimeli İngilizce isim",
            "expected": "Resident Evil normal telaffuz"
        },
        {
            "text": "www.example.com sitesi.",
            "issue": "Web adresinde İngilizce kelime",
            "expected": "example normal telaffuz"
        },
        {
            "text": "Test kelimesi Türkçe, example İngilizce.",
            "issue": "Karışık dil testi",
            "expected": "Her iki kelime de normal telaffuz"
        }
    ]
    
    for i, test in enumerate(english_tests, 1):
        print(f"\n📝 Test {i} - {test['issue']}:")
        print(f"Orijinal: {test['text']}")
        print(f"Beklenen: {test['expected']}")
        
        # Metin işleme testi
        processed = processor.process_text_naturally(test['text'], "tr")
        print(f"İşlenmiş: {processed}")
        
        # TTS testi
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion="neutral"
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/english_fix_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ TTS Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_emotion_fixes():
    """Duygu düzeltmeleri testi"""
    print("\n🎭 Duygu Düzeltmeleri Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    emotion_controller = get_emotion_controller()
    
    # Duygu testleri
    emotion_tests = [
        {
            "text": "<whisper>Bu bir sır... Kimseye söyleme.</whisper>",
            "emotion": "neutral",
            "issue": "Whisper fısıltı efekti",
            "expected": "Kısık, fısıltı ses tonu"
        },
        {
            "text": "Normal konuşma <#1#> bekleme <#2#> uzun bekleme.",
            "emotion": "neutral",
            "issue": "Bekleme kodları sesli okunuyor",
            "expected": "Sessiz beklemeler"
        },
        {
            "text": "<excited>Harika!</excited> <#0.5#> <calm>Sakin devam.</calm>",
            "emotion": "neutral",
            "issue": "Karışık duygu ve bekleme",
            "expected": "Duygu değişimleri ve beklemeler"
        }
    ]
    
    for i, test in enumerate(emotion_tests, 1):
        print(f"\n📝 Test {i} - {test['issue']}:")
        print(f"Metin: {test['text']}")
        print(f"Beklenen: {test['expected']}")
        
        # Duygu işleme testi
        processed_text, segments = emotion_controller.process_emotional_text(
            test['text'], test['emotion'], "tr"
        )
        print(f"İşlenmiş: {processed_text}")
        print(f"Segment sayısı: {len(segments)}")
        
        # TTS testi
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion=test['emotion']
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/emotion_fix_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ TTS Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_comprehensive_fixes():
    """Kapsamlı düzeltmeler testi"""
    print("\n🎬 Kapsamlı Düzeltmeler Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Tüm sorunları içeren test metni
    comprehensive_text = """
    <excited>Merhaba!</excited> RecLastTTS sisteminin düzeltilmiş halini test ediyoruz.
    
    <#1#>
    
    Düzeltilen sorunlar:
    1. Noktalama işleme: Bu bir test. Cümle sonu normal.
    2. İngilizce kelimeler: YouTube, Resident Evil normal telaffuz.
    3. Web adresleri: www.example.com doğru telaffuz.
    4. Ondalık sayılar: 1.5 oranında artış var.
    
    <#1.5#>
    
    <whisper>Bu bir sır... Fısıltı testi yapıyoruz.</whisper>
    <#0.5#>
    <scary>Korkunç hikaye anlatılıyor...</scary>
    <#2#>
    <happy>Mutlu son geldi!</happy>
    
    Dr. Ahmet ve Prof. Mehmet projeyi tamamladı. Teşekkürler.
    
    <calm>Test bitti. Başarılı.</calm>
    """
    
    print("📝 Kapsamlı test metni hazırlandı")
    print(f"📊 Metin uzunluğu: {len(comprehensive_text)} karakter")
    
    start_time = time.time()
    audio_data = engine.text_to_speech(
        text=comprehensive_text,
        language="tr",
        emotion="neutral",
        enable_chunking=True,
        chunk_size=50
    )
    end_time = time.time()
    
    if audio_data is not None:
        output_path = "output/comprehensive_fixes.wav"
        os.makedirs("output", exist_ok=True)
        success = engine.save_audio(audio_data, output_path)
        
        if success:
            duration = len(audio_data) / 22050
            word_count = len(comprehensive_text.split())
            print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
            print(f"📁 Kaydedildi: {output_path}")
            print(f"📊 {word_count} kelime, {word_count / (end_time - start_time):.1f} kelime/saniye")
            print(f"🎯 Tüm düzeltmeleri kontrol edin!")
        else:
            print("❌ Ses kaydedilemedi")
    else:
        print("❌ TTS işlemi başarısız")


def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS Punctuation Fixes Test")
    print("=" * 60)
    print("🎯 Noktalama ve duygu düzeltmeleri test ediliyor...")
    
    try:
        # Testleri çalıştır
        test_punctuation_fixes()
        test_english_word_fixes()
        test_emotion_fixes()
        test_comprehensive_fixes()
        
        print("\n🎉 Tüm düzeltme testleri tamamlandı!")
        print("📁 Ses dosyaları 'output/' klasöründe kaydedildi.")
        print("\n📋 Düzeltilen Sorunlar:")
        print("✅ İngilizce kelime işaretleme kaldırıldı")
        print("✅ Noktalama işleme iyileştirildi")
        print("✅ Whisper parametreleri güçlendirildi")
        print("✅ Bekleme kodları optimize edildi")
        print("✅ Cümle sonu nokta işleme düzeltildi")
        print("\n🎧 Ses dosyalarını dinleyerek iyileştirmeleri kontrol edin!")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

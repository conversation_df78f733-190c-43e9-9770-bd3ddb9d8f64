#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple YouTube Processor
Basit YouTube ses işleme sistemi
"""

import os
import subprocess
import tempfile
import numpy as np
from pathlib import Path
from typing import List, Dict, Optional, Callable
import librosa
import soundfile as sf

from .logger import get_logger

try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False


class SimpleYouTubeProcessor:
    """Basit YouTube ses işleme sistemi"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.target_sample_rate = 22050
        self.segment_duration = 8.0  # 8 saniye ideal fine-tuning süresi
        self.min_segment_duration = 3.0  # Minimum 3 saniye
        self.max_segment_duration = 12.0  # Maximum 12 saniye
        self.progress_callback: Optional[Callable] = None
        
        # Whisper model
        self.whisper_model = None
        if WHISPER_AVAILABLE:
            try:
                self.whisper_model = whisper.load_model("base")
                self.logger.info("✅ Whisper model yüklendi")
            except Exception as e:
                self.logger.warning(f"⚠️ Whisper yüklenemedi: {e}")
        else:
            self.logger.warning("⚠️ Whisper bulunamadı. pip install openai-whisper")
    
    def set_progress_callback(self, callback: Callable):
        """Progress callback ayarla"""
        self.progress_callback = callback
    
    def process_youtube_video(self, youtube_url: str, output_dir: str, speaker_name: str) -> Dict:
        """YouTube videosunu işle"""
        try:
            self.logger.info(f"🎬 YouTube video işleniyor: {youtube_url}")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # 1. Ses dosyasını indir
            if self.progress_callback:
                self.progress_callback("Ses dosyası indiriliyor...", 10)
            
            audio_file = self._download_audio(youtube_url, output_path)
            if not audio_file:
                return {"success": False, "error": "Ses indirilemedi"}
            
            # 2. Ses dosyasını parçalara böl
            if self.progress_callback:
                self.progress_callback("Ses parçalara bölünüyor...", 30)
            
            segments = self._split_audio_to_segments(audio_file, output_path, speaker_name)
            if not segments:
                return {"success": False, "error": "Ses bölünemedi"}
            
            # 3. Her segment için transkript oluştur
            if self.progress_callback:
                self.progress_callback("Transkriptler oluşturuluyor...", 50)
            
            transcribed_segments = self._transcribe_segments(segments)
            
            # 4. Dataset hazırla
            if self.progress_callback:
                self.progress_callback("Dataset hazırlanıyor...", 80)
            
            dataset_result = self._prepare_dataset(transcribed_segments, output_path, speaker_name)
            
            # Geçici dosyaları temizle
            if os.path.exists(audio_file):
                os.remove(audio_file)
            
            if self.progress_callback:
                self.progress_callback("Tamamlandı!", 100)
            
            self.logger.info(f"✅ YouTube işleme tamamlandı: {len(transcribed_segments)} segment")
            
            return {
                "success": True,
                "segments": transcribed_segments,
                "total_segments": len(transcribed_segments),
                "dataset_path": dataset_result.get("dataset_path"),
                "speaker_name": speaker_name
            }
            
        except Exception as e:
            self.logger.error(f"❌ YouTube işleme hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def _download_audio(self, youtube_url: str, output_dir: Path) -> Optional[str]:
        """YouTube'dan ses indir"""
        try:
            # Geçici dosya adı
            temp_file = output_dir / f"temp_audio_{os.getpid()}.wav"
            
            cmd = [
                'yt-dlp',
                '--extract-audio',
                '--audio-format', 'wav',
                '--audio-quality', '0',  # En iyi kalite
                '--output', str(temp_file.with_suffix('.%(ext)s')),
                '--no-playlist',
                youtube_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and temp_file.exists():
                self.logger.info(f"✅ Ses indirildi: {temp_file}")
                return str(temp_file)
            else:
                self.logger.error(f"❌ Ses indirme hatası: {result.stderr}")
                return None
            
        except Exception as e:
            self.logger.error(f"❌ Ses indirme hatası: {e}")
            return None
    
    def _split_audio_to_segments(self, audio_file: str, output_dir: Path, speaker_name: str) -> List[Dict]:
        """Ses dosyasını ideal segmentlere böl"""
        try:
            # Ses dosyasını yükle
            audio, sr = librosa.load(audio_file, sr=self.target_sample_rate, mono=True)
            
            # Sessizlikleri tespit et
            silence_threshold = 0.01
            frame_length = int(0.025 * sr)  # 25ms
            hop_length = int(0.010 * sr)    # 10ms
            
            # Energy hesapla
            energy = []
            for i in range(0, len(audio) - frame_length, hop_length):
                frame = audio[i:i + frame_length]
                rms = np.sqrt(np.mean(frame ** 2))
                energy.append(rms)
            
            energy = np.array(energy)
            
            # Sessizlik noktalarını bul
            silence_frames = energy < silence_threshold
            
            # Segment noktalarını belirle
            segment_points = []
            current_pos = 0
            
            while current_pos < len(audio):
                # İdeal segment uzunluğu
                ideal_end = current_pos + int(self.segment_duration * sr)
                
                if ideal_end >= len(audio):
                    # Son segment
                    if len(audio) - current_pos >= int(self.min_segment_duration * sr):
                        segment_points.append((current_pos, len(audio)))
                    break
                
                # Ideal nokta etrafında sessizlik ara
                search_start = max(0, ideal_end - int(2.0 * sr))  # 2 saniye öncesi
                search_end = min(len(audio), ideal_end + int(2.0 * sr))  # 2 saniye sonrası
                
                # En iyi kesim noktasını bul
                best_cut = ideal_end
                min_energy = float('inf')
                
                for pos in range(search_start, search_end, hop_length):
                    frame_idx = min(pos // hop_length, len(energy) - 1)
                    if energy[frame_idx] < min_energy:
                        min_energy = energy[frame_idx]
                        best_cut = pos
                
                segment_points.append((current_pos, best_cut))
                current_pos = best_cut
            
            # Segmentleri kaydet
            segments = []
            wavs_dir = output_dir / "wavs"
            wavs_dir.mkdir(exist_ok=True)
            
            for i, (start, end) in enumerate(segment_points):
                segment_audio = audio[start:end]
                duration = len(segment_audio) / sr
                
                # Süre kontrolü
                if duration < self.min_segment_duration or duration > self.max_segment_duration:
                    continue
                
                # Dosya adı
                segment_filename = f"{speaker_name}_{i:04d}.wav"
                segment_path = wavs_dir / segment_filename
                
                # Normalize et
                if np.max(np.abs(segment_audio)) > 0:
                    segment_audio = segment_audio / np.max(np.abs(segment_audio)) * 0.95
                
                # Kaydet
                sf.write(segment_path, segment_audio, sr)
                
                segments.append({
                    'audio_file': str(segment_path),
                    'filename': segment_filename,
                    'duration': duration,
                    'start_time': start / sr,
                    'end_time': end / sr
                })
            
            self.logger.info(f"✅ {len(segments)} segment oluşturuldu")
            return segments
            
        except Exception as e:
            self.logger.error(f"❌ Ses bölme hatası: {e}")
            return []
    
    def _transcribe_segments(self, segments: List[Dict]) -> List[Dict]:
        """Segmentleri transkript et"""
        try:
            if not self.whisper_model:
                # Whisper yoksa fake transkript
                self.logger.warning("⚠️ Whisper yok, fake transkript oluşturuluyor")
                for i, segment in enumerate(segments):
                    segment['text'] = f"Bu segment {i+1} numaralı ses örneğidir."
                return segments
            
            transcribed = []
            
            for i, segment in enumerate(segments):
                try:
                    audio_file = segment['audio_file']
                    
                    # Whisper ile transkript
                    result = self.whisper_model.transcribe(
                        audio_file, 
                        language='tr',  # Türkçe
                        word_timestamps=False
                    )
                    
                    text = result['text'].strip()
                    
                    # Metin temizleme
                    text = self._clean_text(text)
                    
                    # Minimum metin uzunluğu kontrolü
                    if len(text) < 10:
                        text = f"Bu {i+1} numaralı ses segmentidir."
                    
                    segment['text'] = text
                    transcribed.append(segment)
                    
                    if (i + 1) % 10 == 0:
                        self.logger.info(f"📝 {i+1} segment transkript edildi")
                    
                except Exception as e:
                    self.logger.warning(f"Segment {i} transkript hatası: {e}")
                    # Hatalı segmentleri atla
                    continue
            
            self.logger.info(f"✅ {len(transcribed)} segment transkript edildi")
            return transcribed
            
        except Exception as e:
            self.logger.error(f"❌ Transkript hatası: {e}")
            return segments
    
    def _clean_text(self, text: str) -> str:
        """Metni temizle"""
        try:
            # Başlangıç ve bitiş boşluklarını kaldır
            text = text.strip()
            
            # Çoklu boşlukları tek yap
            import re
            text = re.sub(r'\s+', ' ', text)
            
            # Özel karakterleri temizle (Türkçe karakterleri koru)
            text = re.sub(r'[^\w\s\.,!?;:-]', '', text)
            
            return text
            
        except:
            return text
    
    def _prepare_dataset(self, segments: List[Dict], output_dir: Path, speaker_name: str) -> Dict:
        """Dataset hazırla"""
        try:
            # Metadata CSV oluştur
            metadata_file = output_dir / "metadata.csv"
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                for segment in segments:
                    filename = segment['filename']
                    text = segment['text']
                    # LJSpeech formatı: filename|text|text
                    f.write(f"wavs/{filename}|{text}|{text}\n")
            
            # Speakers.json oluştur
            speakers_file = output_dir / "speakers.json"
            speakers_config = {
                "speakers": {
                    speaker_name: {
                        "name": speaker_name,
                        "language": "tr",
                        "dataset_path": str(output_dir),
                        "created_by": "RecLastTTS"
                    }
                }
            }
            
            import json
            with open(speakers_file, 'w', encoding='utf-8') as f:
                json.dump(speakers_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ Dataset hazırlandı: {len(segments)} dosya")
            
            return {
                "success": True,
                "dataset_path": str(output_dir),
                "metadata_file": str(metadata_file),
                "speakers_file": str(speakers_file),
                "total_files": len(segments)
            }
            
        except Exception as e:
            self.logger.error(f"❌ Dataset hazırlama hatası: {e}")
            return {"success": False, "error": str(e)}


def get_simple_youtube_processor() -> SimpleYouTubeProcessor:
    """Global simple processor instance"""
    return SimpleYouTubeProcessor()

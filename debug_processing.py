#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Text Processing
Metin işleme debug
"""

from src.reclasttts.utils.turkish_phonetic_processor import get_turkish_phonetic_processor

# Test
processor = get_turkish_phonetic_processor()

test_texts = [
    "Oran 1.5 artmış.",
    "Değer 2.5 hesaplandı.",
    "Sonuç 3.25 çıktı."
]

for text in test_texts:
    print(f"Orijinal: {text}")
    processed = processor.process_text_naturally(text, "tr")
    print(f"İşlenmiş: {processed}")
    print()

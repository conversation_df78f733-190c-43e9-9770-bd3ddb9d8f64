2025-06-05 21:52:59 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 21:53:00 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 21:53:10 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 21:53:10 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 21:53:10 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 21:53:10 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 21:53:19 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 21:53:19 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 21:53:19 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 21:53:19 | reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: Model is multi-speaker but no `speaker` is provided.
2025-06-05 21:53:20 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-05 21:55:17 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 21:55:18 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 21:55:27 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 21:55:27 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 21:55:27 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 21:55:27 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 21:55:37 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 21:55:37 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 21:55:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker ses dosyası oluşturuldu
2025-06-05 21:55:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 21:55:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 21:55:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 21:55:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 21:55:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 21:55:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.424
2025-06-05 21:55:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.673
2025-06-05 21:55:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 21:55:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 32
2025-06-05 21:55:38 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 21:55:38 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 21:55:38 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['RecLastTTS Engine test başarılı!']
2025-06-05 21:55:39 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.064
2025-06-05 21:55:39 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.293
2025-06-05 21:55:39 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 21:55:39 | reclasttts.utils.audio_utils | [31mERROR[0m | Ses kaydetme hatası: 'list' object has no attribute 'dtype'
2025-06-05 21:55:39 | reclasttts.core.tts_engine | [31mERROR[0m | Ses dosyası kaydedilemedi: output/test_engine.wav
2025-06-05 21:55:39 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | 🎙️ RecLastTTS Model İndirme ve Optimizasyon
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | ==================================================
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | 🎮 GPU Bilgileri:
2025-06-05 21:59:04 | __main__ | [32mINFO[0m |   - GPU: NVIDIA GeForce RTX 4060 Laptop GPU
2025-06-05 21:59:04 | __main__ | [32mINFO[0m |   - VRAM: 8.0GB
2025-06-05 21:59:04 | __main__ | [32mINFO[0m |   - CUDA: 12.1
2025-06-05 21:59:04 | __main__ | [32mINFO[0m |   - GPU Sayısı: 1
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | ⚙️ GPU optimizasyonu yapılıyor...
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | 🔧 GPU bellek fraksiyonu: 0.8
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | ✅ GPU optimizasyonu tamamlandı
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | 
📥 Model indirme işlemi başlıyor...
2025-06-05 21:59:04 | __main__ | [32mINFO[0m | 📥 XTTS v2 modeli indiriliyor...
2025-06-05 21:59:04 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 21:59:05 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 21:59:14 | __main__ | [32mINFO[0m | ✅ Model indirildi (9.4s)
2025-06-05 21:59:14 | __main__ | [32mINFO[0m | 🎮 GPU Bilgileri:
2025-06-05 21:59:14 | __main__ | [32mINFO[0m |   - GPU: NVIDIA GeForce RTX 4060 Laptop GPU
2025-06-05 21:59:14 | __main__ | [32mINFO[0m |   - VRAM: 8.0GB
2025-06-05 21:59:14 | __main__ | [32mINFO[0m |   - CUDA: 12.1
2025-06-05 21:59:14 | __main__ | [32mINFO[0m |   - GPU Sayısı: 1
2025-06-05 21:59:14 | __main__ | [32mINFO[0m | 🔥 Model GPU'ya yükleniyor...
2025-06-05 21:59:14 | __main__ | [32mINFO[0m | ✅ Model GPU'ya yüklendi (0.4s)
2025-06-05 21:59:14 | __main__ | [31mERROR[0m | ❌ Model indirme hatası: name 'torch' is not defined
2025-06-05 21:59:14 | __main__ | [31mERROR[0m | ❌ Model indirilemedi
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | 🎙️ RecLastTTS Model İndirme ve Optimizasyon
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | ==================================================
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | 🎮 GPU Bilgileri:
2025-06-05 22:00:10 | __main__ | [32mINFO[0m |   - GPU: NVIDIA GeForce RTX 4060 Laptop GPU
2025-06-05 22:00:10 | __main__ | [32mINFO[0m |   - VRAM: 8.0GB
2025-06-05 22:00:10 | __main__ | [32mINFO[0m |   - CUDA: 12.1
2025-06-05 22:00:10 | __main__ | [32mINFO[0m |   - GPU Sayısı: 1
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | ⚙️ GPU optimizasyonu yapılıyor...
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | 🔧 GPU bellek fraksiyonu: 0.8
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | ✅ GPU optimizasyonu tamamlandı
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | 
📥 Model indirme işlemi başlıyor...
2025-06-05 22:00:10 | __main__ | [32mINFO[0m | 📥 XTTS v2 modeli indiriliyor...
2025-06-05 22:00:10 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 22:00:11 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 22:00:19 | __main__ | [32mINFO[0m | ✅ Model indirildi (9.2s)
2025-06-05 22:00:19 | __main__ | [32mINFO[0m | 🎮 GPU Bilgileri:
2025-06-05 22:00:19 | __main__ | [32mINFO[0m |   - GPU: NVIDIA GeForce RTX 4060 Laptop GPU
2025-06-05 22:00:19 | __main__ | [32mINFO[0m |   - VRAM: 8.0GB
2025-06-05 22:00:19 | __main__ | [32mINFO[0m |   - CUDA: 12.1
2025-06-05 22:00:19 | __main__ | [32mINFO[0m |   - GPU Sayısı: 1
2025-06-05 22:00:19 | __main__ | [32mINFO[0m | 🔥 Model GPU'ya yükleniyor...
2025-06-05 22:00:20 | __main__ | [32mINFO[0m | ✅ Model GPU'ya yüklendi (0.4s)
2025-06-05 22:00:20 | __main__ | [32mINFO[0m | 📊 GPU Bellek: 1.8GB allocated, 1.8GB cached
2025-06-05 22:00:20 | __main__ | [32mINFO[0m | 
📁 Model yerel klasöre kopyalanıyor...
2025-06-05 22:00:20 | __main__ | [32mINFO[0m | 📁 Yerel model klasörü: models\xtts_v2
2025-06-05 22:00:20 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 22:00:21 | __main__ | [31mERROR[0m | ❌ Model kopyalama hatası: exists: path should be string, bytes, os.PathLike or integer, not tuple
2025-06-05 22:00:21 | __main__ | [33mWARNING[0m | ⚠️ Model kopyalanamadı, cache'den çalışacak
2025-06-05 22:00:21 | __main__ | [32mINFO[0m | 
🧪 Performans testi yapılıyor...
2025-06-05 22:00:21 | __main__ | [32mINFO[0m | 🧪 Model performans testi...
2025-06-05 22:00:21 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 22:00:21 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 22:00:21 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 22:00:21 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 22:00:30 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 22:00:30 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 22:00:30 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 22:00:30 | __main__ | [32mINFO[0m | ⚡ Engine başlatma: 9.7s
2025-06-05 22:00:30 | __main__ | [32mINFO[0m | 🎤 Test 1: 9 karakter
2025-06-05 22:00:30 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 9
2025-06-05 22:00:30 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 22:00:30 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 22:00:30 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Kısa test']
2025-06-05 22:00:32 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.696
2025-06-05 22:00:32 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.159
2025-06-05 22:00:32 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 22:00:32 | __main__ | [32mINFO[0m |   ✅ Başarılı: 1.70s, 5.3 kar/s, RTF: 0.86x
2025-06-05 22:00:32 | __main__ | [32mINFO[0m | 🎤 Test 2: 36 karakter
2025-06-05 22:00:32 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 36
2025-06-05 22:00:32 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 22:00:32 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 22:00:32 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu orta uzunlukta bir test metnidir.']
2025-06-05 22:00:34 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.207
2025-06-05 22:00:34 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.398
2025-06-05 22:00:34 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 22:00:34 | __main__ | [32mINFO[0m |   ✅ Başarılı: 2.21s, 16.3 kar/s, RTF: 2.51x
2025-06-05 22:00:34 | __main__ | [32mINFO[0m | 🎤 Test 3: 116 karakter
2025-06-05 22:00:34 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 116
2025-06-05 22:00:34 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 22:00:34 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 22:00:34 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir test metnidir.', 'RecLastTTS sisteminin performansını ölçmek için kullanılıyor.', 'Türkçe karakterler: ğüşıöç.']
2025-06-05 22:00:40 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.246
2025-06-05 22:00:40 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.402
2025-06-05 22:00:40 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 22:00:40 | __main__ | [32mINFO[0m |   ✅ Başarılı: 5.26s, 22.1 kar/s, RTF: 2.49x
2025-06-05 22:00:40 | __main__ | [32mINFO[0m | 📊 Ortalama performans: 17.6 karakter/saniye
2025-06-05 22:00:40 | __main__ | [32mINFO[0m | 📊 GPU Bellek kullanımı: 3.6GB / 3.6GB
2025-06-05 22:00:40 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-05 22:00:40 | reclasttts.core.tts_engine | [31mERROR[0m | Temizleme hatası: 'RecLastTTSEngine' object has no attribute 'tts_model'
2025-06-05 22:00:40 | __main__ | [32mINFO[0m | 
🎉 Model indirme ve optimizasyon tamamlandı!
2025-06-05 22:00:40 | __main__ | [32mINFO[0m | ✅ RecLastTTS kullanıma hazır
2025-06-05 22:57:20 | reclasttts.api.server | [32mINFO[0m | 🌐 Server başlatılıyor: http://127.0.0.1:8000
2025-06-05 22:57:20 | reclasttts.api.server | [32mINFO[0m | 📚 API Dokümantasyonu: http://127.0.0.1:8000/docs
2025-06-05 22:57:21 | reclasttts.api.server | [32mINFO[0m | 🚀 RecLastTTS API Server başlatılıyor...
2025-06-05 22:57:21 | reclasttts.api.server | [32mINFO[0m | 📥 TTS Engine önceden yükleniyor...
2025-06-05 22:57:21 | reclasttts.api.endpoints | [32mINFO[0m | TTS Engine başlatılıyor...
2025-06-05 22:57:21 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 22:57:21 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 22:57:21 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 22:57:21 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 22:57:31 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 22:57:31 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 22:57:31 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 22:57:31 | reclasttts.api.server | [32mINFO[0m | ✅ TTS Engine hazır
2025-06-05 22:57:31 | reclasttts.api.server | [32mINFO[0m | 📥 Voice Cloner önceden yükleniyor...
2025-06-05 22:57:31 | reclasttts.api.endpoints | [32mINFO[0m | Voice Cloner başlatılıyor...
2025-06-05 22:57:31 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-05 22:57:31 | reclasttts.api.server | [32mINFO[0m | ✅ Voice Cloner hazır
2025-06-05 22:57:31 | reclasttts.api.server | [32mINFO[0m | 🎉 RecLastTTS API Server başlatıldı!
2025-06-05 22:57:32 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 22:57:32 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 22:57:32 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 22:57:32 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 22:57:33 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.347
2025-06-05 22:57:33 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.725
2025-06-05 22:57:33 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 22:57:33 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-05 22:57:33 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 22:57:33 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 22:57:33 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['RecLastTTS API test başarılı!']
2025-06-05 22:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.368
2025-06-05 22:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.357
2025-06-05 22:57:34 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 22:57:34 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmpd8brvwa7.wav
2025-06-05 22:57:34 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 33
2025-06-05 22:57:34 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-05 22:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 22:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu klonlanmış sesle konuşma testi']
2025-06-05 22:57:36 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.130
2025-06-05 22:57:36 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.330
2025-06-05 22:57:36 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 22:57:36 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmp6yzbelrk.wav
2025-06-05 23:12:48 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-06-05 23:12:48 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:13:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:14] "GET / HTTP/1.1" 200 -
2025-06-05 23:13:14 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:13:14 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:13:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:14] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:13:15 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:13:16 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:13:26 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:13:26 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:13:26 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:13:26 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:13:26 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:13:26 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:13:26 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.386
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.256
2025-06-05 23:13:28 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:13:28 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:28] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:13:28 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:13:28 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.475
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.362
2025-06-05 23:13:28 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:13:28 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:28] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:13:28 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:13:28 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:13:28 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:13:30 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.383
2025-06-05 23:13:30 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.490
2025-06-05 23:13:30 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:13:30 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:30] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:13:46 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-05 23:13:46 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:13:46 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:13:46 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test ses bakalım deneylim 123']
2025-06-05 23:13:46 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:46] "GET / HTTP/1.1" 200 -
2025-06-05 23:13:47 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:13:47 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:13:47 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:13:47 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:13:47 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.665
2025-06-05 23:13:47 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.530
2025-06-05 23:13:47 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:13:47 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:47] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:13:48 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.857
2025-06-05 23:13:48 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.443
2025-06-05 23:13:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:13:48 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmpqa_ctqao.wav
2025-06-05 23:13:48 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:13:48] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:16:47 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:16:47] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:22:55 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:22:55 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:22:55 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:23:23 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:23:23] "GET / HTTP/1.1" 200 -
2025-06-05 23:23:23 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:23:23] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:23:23 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:23:23 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:23:23 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:23:24 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:23:33 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:23:33 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 28
2025-06-05 23:23:33 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:23:33 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:23:33 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba Test edelim bakalım.']
2025-06-05 23:23:34 | reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-06-05 23:23:34 | reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-06-05 23:23:34 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:23:34] "[35m[1mPOST /api/tts HTTP/1.1[0m" 500 -
2025-06-05 23:23:34 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:23:34] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:27:24 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:27:24 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:27:24 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:27:58 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:27:58] "GET / HTTP/1.1" 200 -
2025-06-05 23:27:58 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cpu
2025-06-05 23:27:58 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:27:58 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:27:59 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:28:10 | reclasttts.core.tts_engine | [32mINFO[0m | Model CPU'da çalışıyor
2025-06-05 23:28:10 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:28:10 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker ses dosyası oluşturuldu
2025-06-05 23:28:10 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:28:10 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:28:10 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:28:10 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:28:10 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:28:13 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.144
2025-06-05 23:28:13 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.893
2025-06-05 23:28:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:28:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:28:13] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:28:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:28:13 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:28:13 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:28:13 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:28:15 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-05 23:28:15 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:28:15 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:28:15 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.']
2025-06-05 23:28:16 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.890
2025-06-05 23:28:16 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.857
2025-06-05 23:28:16 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:28:16 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:28:16] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:28:27 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 11.229
2025-06-05 23:28:27 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 2.023
2025-06-05 23:28:27 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:28:27 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmp24_dhc2p.wav
2025-06-05 23:28:27 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:28:27] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:28:28 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:28:28] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:30:03 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:30:04 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:30:04 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:30:06 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:30:06] "GET / HTTP/1.1" 200 -
2025-06-05 23:30:06 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:30:06 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:30:06 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:30:07 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:30:15 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:30:15 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:30:15 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:30:16 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:30:20 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:30:20 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:30:20 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker ses dosyası oluşturuldu
2025-06-05 23:30:20 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:30:20 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:30:20 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:30:20 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:30:20 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:30:22 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.638
2025-06-05 23:30:22 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.835
2025-06-05 23:30:22 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:30:22 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:30:22] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:30:27 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:30:27 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:30:27 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:30:27 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-05 23:30:27 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 28
2025-06-05 23:30:27 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:30:27 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:30:27 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba Test edelim bakalım.']
2025-06-05 23:30:30 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.967
2025-06-05 23:30:30 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.550
2025-06-05 23:30:30 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:30:30 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmpk82etq54.wav
2025-06-05 23:30:30 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:30:30] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:30:33 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:30:33] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:39:15 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:39:15 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:39:15 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:41:15 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:41:15] "GET / HTTP/1.1" 200 -
2025-06-05 23:41:15 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:41:15 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:41:15 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:41:16 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:41:24 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:41:24 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:41:24 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:41:25 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:41:30 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:41:30 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:41:30 | reclasttts.core.tts_engine | [32mINFO[0m | Kaliteli varsayılan ses indiriliyor...
2025-06-05 23:41:31 | reclasttts.core.tts_engine | [32mINFO[0m | Kaliteli varsayılan speaker ses dosyası indirildi
2025-06-05 23:41:31 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:41:31 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:41:31 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:41:31 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:41:31 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:41:32 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.211
2025-06-05 23:41:32 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.053
2025-06-05 23:41:32 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:41:32 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:41:32] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:41:36 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:41:36 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:41:36 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:41:36 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-05 23:41:36 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-05 23:41:36 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:41:36 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:41:36 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba.', 'Test edelim bakalım.']
2025-06-05 23:41:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.225
2025-06-05 23:41:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.450
2025-06-05 23:41:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:41:38 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmpyfsnbej7.wav
2025-06-05 23:41:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:41:38] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:41:42 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:41:42] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:42:00 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 23
2025-06-05 23:42:00 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:42:00 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:42:00 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Hello Guys how are you.']
2025-06-05 23:42:01 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.984
2025-06-05 23:42:01 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.529
2025-06-05 23:42:01 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:42:01 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmp8erioyr1.wav
2025-06-05 23:42:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:42:01] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:48:22 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:48:22 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:48:22 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:48:25 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:48:25] "GET / HTTP/1.1" 200 -
2025-06-05 23:48:25 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:48:25] "[33mGET /static/logo.png HTTP/1.1[0m" 404 -
2025-06-05 23:48:25 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:48:25 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:48:25 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:48:25] "[33mGET /static/logo.ico HTTP/1.1[0m" 404 -
2025-06-05 23:48:25 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:48:26 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:48:37 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:48:37 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:48:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:48:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:48:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:48:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:48:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:48:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.228
2025-06-05 23:48:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.017
2025-06-05 23:48:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:48:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:48:38] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:50:51 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:50:51 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:50:51 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:50:53 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:50:53] "GET / HTTP/1.1" 200 -
2025-06-05 23:50:53 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:50:53] "[33mGET /static/logo.png HTTP/1.1[0m" 404 -
2025-06-05 23:50:53 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:50:53 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:50:53 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:50:53] "[33mGET /static/favicon.ico HTTP/1.1[0m" 404 -
2025-06-05 23:50:53 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:50:54 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:51:04 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:51:04 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:51:04 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:51:04 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:51:04 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:51:04 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:51:04 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:51:05 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.231
2025-06-05 23:51:05 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.938
2025-06-05 23:51:05 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:51:05 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:51:05] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:53:33 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-05 23:53:33 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-05 23:53:33 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-05 23:53:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:38] "GET / HTTP/1.1" 200 -
2025-06-05 23:53:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:38] "GET /assets/logo.png HTTP/1.1" 200 -
2025-06-05 23:53:38 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-05 23:53:38 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-05 23:53:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:38] "GET /assets/logo.ico HTTP/1.1" 200 -
2025-06-05 23:53:38 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-05 23:53:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:38] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-05 23:53:39 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-05 23:53:48 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-05 23:53:48 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-05 23:53:48 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-05 23:53:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-05 23:53:48 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:53:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:53:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-05 23:53:50 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.305
2025-06-05 23:53:50 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.994
2025-06-05 23:53:50 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:53:50 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:50] "GET /api/status HTTP/1.1" 200 -
2025-06-05 23:53:50 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 41
2025-06-05 23:53:50 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:53:50 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:53:50 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım başarılı mı?']
2025-06-05 23:53:52 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.495
2025-06-05 23:53:52 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.429
2025-06-05 23:53:52 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:53:52 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmptt6_36mq.wav
2025-06-05 23:53:52 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:52] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:53:55 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:53:55] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-05 23:54:12 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 24
2025-06-05 23:54:12 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-05 23:54:12 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:54:12 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Hello guys.', 'How are you?']
2025-06-05 23:54:13 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.887
2025-06-05 23:54:13 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.298
2025-06-05 23:54:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:54:13 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmp0m8vrnbv.wav
2025-06-05 23:54:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:54:13] "POST /api/tts HTTP/1.1" 200 -
2025-06-05 23:54:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 33
2025-06-05 23:54:37 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-05 23:54:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-05 23:54:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bakalım klonlama nasıl çalışacak.']
2025-06-05 23:54:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.903
2025-06-05 23:54:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.308
2025-06-05 23:54:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-05 23:54:38 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\AppData\Local\Temp\tmpoz4wiwei.wav
2025-06-05 23:54:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [05/Jun/2025 23:54:38] "POST /api/clone HTTP/1.1" 200 -
2025-06-06 00:05:59 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 00:05:59 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 00:05:59 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 00:06:02 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:06:02] "GET / HTTP/1.1" 200 -
2025-06-06 00:06:02 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:06:02] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 00:06:02 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:06:02 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:06:02 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:06:02 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:06:02] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:06:02 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:06:03 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:06:13 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:06:13 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:06:13 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:06:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-06 00:06:13 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:06:13 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:06:13 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:06:14 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.198
2025-06-06 00:06:14 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.991
2025-06-06 00:06:14 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:06:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:06:14] "GET /api/status HTTP/1.1" 200 -
2025-06-06 00:06:21 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-06 00:06:21 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-06 00:06:21 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:06:21 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:06:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:06:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:06:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.']
2025-06-06 00:06:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.']
2025-06-06 00:06:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.065
2025-06-06 00:06:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.681
2025-06-06 00:06:23 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:06:23 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output\tts_output_1749157583.wav
2025-06-06 00:06:23 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:06:23] "[35m[1mPOST /api/tts HTTP/1.1[0m" 500 -
2025-06-06 00:06:24 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.975
2025-06-06 00:06:24 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.607
2025-06-06 00:06:24 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:06:24 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output\tts_output_1749157584.wav
2025-06-06 00:06:24 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:06:24] "[35m[1mPOST /api/tts HTTP/1.1[0m" 500 -
2025-06-06 00:08:07 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 00:08:07 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 00:08:07 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 00:08:09 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:09] "GET / HTTP/1.1" 200 -
2025-06-06 00:08:09 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:09] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 00:08:09 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:08:09 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:08:09 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:08:09 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:09] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:08:09 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:08:10 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:08:19 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:08:19 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:08:19 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:08:19 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-06 00:08:19 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:08:19 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:08:19 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:08:20 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.235
2025-06-06 00:08:20 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.022
2025-06-06 00:08:20 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:08:20 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:20] "GET /api/status HTTP/1.1" 200 -
2025-06-06 00:08:21 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-06 00:08:21 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29
2025-06-06 00:08:21 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:08:21 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:08:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:08:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:08:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.']
2025-06-06 00:08:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.']
2025-06-06 00:08:22 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.633
2025-06-06 00:08:22 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.676
2025-06-06 00:08:22 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:08:22 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749157702.wav
2025-06-06 00:08:22 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:22] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:08:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.207
2025-06-06 00:08:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.576
2025-06-06 00:08:23 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:08:23 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749157703.wav
2025-06-06 00:08:23 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:23] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:08:26 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:26] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 00:08:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 18
2025-06-06 00:08:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 18
2025-06-06 00:08:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:08:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:08:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:08:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:08:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Hello How are you.']
2025-06-06 00:08:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Hello How are you.']
2025-06-06 00:08:39 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.305
2025-06-06 00:08:39 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.540
2025-06-06 00:08:39 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:08:39 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749157719.wav
2025-06-06 00:08:39 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:39] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:08:39 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.946
2025-06-06 00:08:39 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.433
2025-06-06 00:08:39 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:08:39 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749157719.wav
2025-06-06 00:08:39 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:08:39] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:09:18 | reclasttts.core.voice_cloner | [32mINFO[0m | Referans dosyası işlendi: reference_1.wav
2025-06-06 00:09:18 | reclasttts.core.voice_cloner | [32mINFO[0m | ✅ Ses klonu oluşturuldu: Umit
2025-06-06 00:09:18 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:09:18] "POST /api/voices/create HTTP/1.1" 200 -
2025-06-06 00:09:18 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:09:18] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:09:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 52
2025-06-06 00:09:38 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:09:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 52
2025-06-06 00:09:38 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:09:38 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:09:38 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test edelim bakalım.', 'Nasıl çalışacak merak ediyorum.']
2025-06-06 00:09:38 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:09:38 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test edelim bakalım.', 'Nasıl çalışacak merak ediyorum.']
2025-06-06 00:09:44 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 6.371
2025-06-06 00:09:44 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.731
2025-06-06 00:09:44 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:09:44 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749157784.wav
2025-06-06 00:09:44 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:09:44] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:09:46 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 7.847
2025-06-06 00:09:46 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.589
2025-06-06 00:09:46 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:09:46 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749157786.wav
2025-06-06 00:09:46 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:09:46] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:10:09 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 20
2025-06-06 00:10:09 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:10:09 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:10:09 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test edelim bakalım.']
2025-06-06 00:10:10 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.141
2025-06-06 00:10:10 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.453
2025-06-06 00:10:10 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:10:10 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\cloned_voice_1749157810.wav
2025-06-06 00:10:10 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:10:10] "POST /api/clone HTTP/1.1" 200 -
2025-06-06 00:14:27 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 00:14:27 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 00:14:27 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 00:14:48 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:14:48] "GET / HTTP/1.1" 200 -
2025-06-06 00:14:48 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:14:48] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 00:14:48 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:14:48 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:14:48 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:14:48 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:14:48] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:14:48 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:14:49 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:14:59 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:14:59 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:14:59 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:14:59 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-06 00:14:59 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:14:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:14:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:15:00 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.657
2025-06-06 00:15:00 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.844
2025-06-06 00:15:00 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:15:00 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:15:00] "GET /api/status HTTP/1.1" 200 -
2025-06-06 00:15:03 | reclasttts.core.voice_cloner | [32mINFO[0m | Minimal işleme tamamlandı: processed_1.wav
2025-06-06 00:15:03 | reclasttts.core.voice_cloner | [32mINFO[0m | Referans dosyası işlendi: reference_1.wav
2025-06-06 00:15:03 | reclasttts.core.voice_cloner | [32mINFO[0m | ✅ Ses klonu oluşturuldu: Umit
2025-06-06 00:15:03 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:15:03] "POST /api/voices/create HTTP/1.1" 200 -
2025-06-06 00:15:03 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:15:03] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:15:19 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:15:19 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:15:19 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37
2025-06-06 00:15:19 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37
2025-06-06 00:15:19 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:15:19 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:15:19 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:15:19 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:15:19 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test edelim bakalım.', 'Nasıl çalışıyor.']
2025-06-06 00:15:19 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test edelim bakalım.', 'Nasıl çalışıyor.']
2025-06-06 00:15:22 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.017
2025-06-06 00:15:22 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.740
2025-06-06 00:15:22 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:15:22 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158122.wav
2025-06-06 00:15:22 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:15:22] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:15:22 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.403
2025-06-06 00:15:22 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.704
2025-06-06 00:15:22 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:15:22 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158122.wav
2025-06-06 00:15:22 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:15:22] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:15:24 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:15:24] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 00:17:03 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:17:03 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:17:03 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (701 karakter), kısaltılıyor
2025-06-06 00:17:03 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 500
2025-06-06 00:17:03 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:17:03 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (701 karakter), kısaltılıyor
2025-06-06 00:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:17:03 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 500
2025-06-06 00:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['projeye başlamak için tüm bu detayları özetleyen,hangi teknolojilerin kullanılacağı ve yapıyı kurgulayan.', 'mimariyi tasarlayan tüm yapıyı bana verebilirmisin.', 'Cursor da çalışmaya başlayacağım ve buna vereceğim.', 'Tüm yapıyı bir md dosyasında özetle ki cursor bunun üzerine çalışsın.', 'Ayrıca listeler halinde yapılması gerekenleride yaz ki sırayla herşeyi yaparak gitsin.', 'Projenin ve yapmak istediklerimizin özeti, detaylar, teknolojiler, mimari, dosya yapısı, yapılacak listesi herşeyi ile tam bir döküma']
2025-06-06 00:17:03 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['projeye başlamak için tüm bu detayları özetleyen,hangi teknolojilerin kullanılacağı ve yapıyı kurgulayan.', 'mimariyi tasarlayan tüm yapıyı bana verebilirmisin.', 'Cursor da çalışmaya başlayacağım ve buna vereceğim.', 'Tüm yapıyı bir md dosyasında özetle ki cursor bunun üzerine çalışsın.', 'Ayrıca listeler halinde yapılması gerekenleride yaz ki sırayla herşeyi yaparak gitsin.', 'Projenin ve yapmak istediklerimizin özeti, detaylar, teknolojiler, mimari, dosya yapısı, yapılacak listesi herşeyi ile tam bir döküma']
2025-06-06 00:17:29 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 25.843
2025-06-06 00:17:29 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.554
2025-06-06 00:17:29 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:17:29 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158249.wav
2025-06-06 00:17:29 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:17:29] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:17:31 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 27.239
2025-06-06 00:17:31 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.537
2025-06-06 00:17:31 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:17:31 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158251.wav
2025-06-06 00:17:31 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:17:31] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:18:35 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:18:35 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:18:35 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 47
2025-06-06 00:18:35 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:18:35 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:18:35 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: en, Uzunluk: 47
2025-06-06 00:18:35 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test diyoruz.', 'Fena değil gibiydi neden bozuldu.']
2025-06-06 00:18:35 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:18:35 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:18:35 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test diyoruz.', 'Fena değil gibiydi neden bozuldu.']
2025-06-06 00:18:37 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.815
2025-06-06 00:18:37 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.532
2025-06-06 00:18:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:18:37 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158317.wav
2025-06-06 00:18:37 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:18:37] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:18:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.966
2025-06-06 00:18:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.526
2025-06-06 00:18:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:18:38 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158318.wav
2025-06-06 00:18:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:18:38] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:18:45 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:18:45 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:18:45 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (701 karakter), kısaltılıyor
2025-06-06 00:18:45 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500
2025-06-06 00:18:45 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:18:45 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (701 karakter), kısaltılıyor
2025-06-06 00:18:45 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:18:45 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500
2025-06-06 00:18:45 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['projeye başlamak için tüm bu detayları özetleyen,hangi teknolojilerin kullanılacağı ve yapıyı kurgulayan.', 'mimariyi tasarlayan tüm yapıyı bana verebilirmisin.', 'Cursor da çalışmaya başlayacağım ve buna vereceğim.', 'Tüm yapıyı bir md dosyasında özetle ki cursor bunun üzerine çalışsın.', 'Ayrıca listeler halinde yapılması gerekenleride yaz ki sırayla herşeyi yaparak gitsin.', 'Projenin ve yapmak istediklerimizin özeti, detaylar, teknolojiler, mimari, dosya yapısı, yapılacak listesi herşeyi ile tam bir döküma']
2025-06-06 00:18:45 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:18:45 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:18:45 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['projeye başlamak için tüm bu detayları özetleyen,hangi teknolojilerin kullanılacağı ve yapıyı kurgulayan.', 'mimariyi tasarlayan tüm yapıyı bana verebilirmisin.', 'Cursor da çalışmaya başlayacağım ve buna vereceğim.', 'Tüm yapıyı bir md dosyasında özetle ki cursor bunun üzerine çalışsın.', 'Ayrıca listeler halinde yapılması gerekenleride yaz ki sırayla herşeyi yaparak gitsin.', 'Projenin ve yapmak istediklerimizin özeti, detaylar, teknolojiler, mimari, dosya yapısı, yapılacak listesi herşeyi ile tam bir döküma']
2025-06-06 00:19:06 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 20.543
2025-06-06 00:19:06 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.541
2025-06-06 00:19:06 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:19:06 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158346.wav
2025-06-06 00:19:06 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:19:06] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:19:06 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 21.094
2025-06-06 00:19:06 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.536
2025-06-06 00:19:06 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:19:06 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749158346.wav
2025-06-06 00:19:06 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:19:06] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:22:09 | reclasttts.api.server | [32mINFO[0m | 🌐 Server başlatılıyor: http://0.0.0.0:8000
2025-06-06 00:22:09 | reclasttts.api.server | [32mINFO[0m | 📚 API Dokümantasyonu: http://0.0.0.0:8000/docs
2025-06-06 00:22:09 | reclasttts.api.server | [32mINFO[0m | 🚀 RecLastTTS API Server başlatılıyor...
2025-06-06 00:22:09 | reclasttts.api.server | [32mINFO[0m | 📥 TTS Engine önceden yükleniyor...
2025-06-06 00:22:09 | reclasttts.api.endpoints | [32mINFO[0m | TTS Engine başlatılıyor...
2025-06-06 00:22:09 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:22:09 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:22:09 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:22:09 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:22:19 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:22:19 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:22:19 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:22:19 | reclasttts.api.server | [32mINFO[0m | ✅ TTS Engine hazır
2025-06-06 00:22:19 | reclasttts.api.server | [32mINFO[0m | 📥 Voice Cloner önceden yükleniyor...
2025-06-06 00:22:19 | reclasttts.api.endpoints | [32mINFO[0m | Voice Cloner başlatılıyor...
2025-06-06 00:22:19 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:22:19 | reclasttts.api.server | [32mINFO[0m | ✅ Voice Cloner hazır
2025-06-06 00:22:19 | reclasttts.api.server | [32mINFO[0m | 🎉 RecLastTTS API Server başlatıldı!
2025-06-06 00:23:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-06 00:23:13 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:23:13 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:23:13 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:23:14 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.712
2025-06-06 00:23:14 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.828
2025-06-06 00:23:14 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:38:54 | reclasttts.api.server | [32mINFO[0m | 🌐 Server başlatılıyor: http://0.0.0.0:8000
2025-06-06 00:38:54 | reclasttts.api.server | [32mINFO[0m | 📚 API Dokümantasyonu: http://0.0.0.0:8000/docs
2025-06-06 00:38:54 | reclasttts.api.server | [32mINFO[0m | 🚀 RecLastTTS API Server başlatılıyor...
2025-06-06 00:38:54 | reclasttts.api.server | [32mINFO[0m | 📥 TTS Engine önceden yükleniyor...
2025-06-06 00:38:54 | reclasttts.api.endpoints | [32mINFO[0m | TTS Engine başlatılıyor...
2025-06-06 00:38:54 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:38:54 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:38:54 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:38:54 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:39:04 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:39:04 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:39:04 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:39:04 | reclasttts.api.server | [32mINFO[0m | ✅ TTS Engine hazır
2025-06-06 00:39:04 | reclasttts.api.server | [32mINFO[0m | 📥 Voice Cloner önceden yükleniyor...
2025-06-06 00:39:04 | reclasttts.api.endpoints | [32mINFO[0m | Voice Cloner başlatılıyor...
2025-06-06 00:39:04 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:39:04 | reclasttts.api.server | [32mINFO[0m | ✅ Voice Cloner hazır
2025-06-06 00:39:04 | reclasttts.api.server | [32mINFO[0m | 🎉 RecLastTTS API Server başlatıldı!
2025-06-06 00:40:17 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4
2025-06-06 00:40:18 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:40:18 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:40:18 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:40:19 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.793
2025-06-06 00:40:19 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.892
2025-06-06 00:40:19 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:40:19 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 33
2025-06-06 00:40:19 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:40:19 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:40:19 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba dünya!', 'API test ediyoruz.']
2025-06-06 00:40:21 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.242
2025-06-06 00:40:21 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.290
2025-06-06 00:40:21 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:40:21 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\api_tts_output_1749159621.wav
2025-06-06 00:40:21 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:40:21 | reclasttts.api.endpoints | [32mINFO[0m | Ses klonu kullanılıyor: Umit (1 dosya)
2025-06-06 00:40:21 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 54
2025-06-06 00:40:21 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:40:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:40:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, ben Umit.', 'API ile ses klonlama test ediyoruz.']
2025-06-06 00:40:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.012
2025-06-06 00:40:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.317
2025-06-06 00:40:23 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:40:23 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\api_tts_output_1749159623.wav
2025-06-06 00:50:58 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 00:50:58 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 00:50:58 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 00:51:03 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:03] "GET / HTTP/1.1" 200 -
2025-06-06 00:51:03 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:03] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 00:51:03 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:51:03 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:51:03 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:51:03 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:03] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:51:03 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:51:04 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:51:14 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:51:14 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:51:14 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:51:14 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 19
2025-06-06 00:51:14 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:51:14 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:51:14 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:51:16 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.361
2025-06-06 00:51:16 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.037
2025-06-06 00:51:16 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:51:16 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:16] "GET /api/status HTTP/1.1" 200 -
2025-06-06 00:51:22 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:51:22 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:51:22 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 65 -> 140
2025-06-06 00:51:22 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:51:22 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 65 -> 140
2025-06-06 00:51:22 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:51:22 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:51:22 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba Test edelim bakalım Nasıl çalıştığını görelim birlikte']
2025-06-06 00:51:22 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:51:22 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba Test edelim bakalım Nasıl çalıştığını görelim birlikte']
2025-06-06 00:51:26 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.582
2025-06-06 00:51:26 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.717
2025-06-06 00:51:26 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:51:26 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160286.wav
2025-06-06 00:51:26 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:26] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:51:26 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.748
2025-06-06 00:51:26 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.708
2025-06-06 00:51:26 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:51:26 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160286.wav
2025-06-06 00:51:26 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:26] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:51:28 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:51:28] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 00:51:59 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 49 -> 104
2025-06-06 00:51:59 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:51:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:51:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu nasıl çalışıyor bakalım Birde bunu deneyelim']
2025-06-06 00:52:01 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.240
2025-06-06 00:52:01 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.569
2025-06-06 00:52:01 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:52:01 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\cloned_voice_1749160321.wav
2025-06-06 00:52:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:52:01] "POST /api/clone HTTP/1.1" 200 -
2025-06-06 00:57:09 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 00:57:09 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 00:57:09 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 00:57:17 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:17] "GET / HTTP/1.1" 200 -
2025-06-06 00:57:17 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:17] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 00:57:18 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 00:57:18 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 00:57:18 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 00:57:18 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:18] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 00:57:18 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 00:57:18 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 00:57:28 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 00:57:28 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 00:57:28 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 00:57:28 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-06 00:57:28 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:57:28 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:57:28 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 00:57:30 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.211
2025-06-06 00:57:30 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.965
2025-06-06 00:57:30 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:57:30 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:30] "GET /api/status HTTP/1.1" 200 -
2025-06-06 00:57:32 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 70 -> 71
2025-06-06 00:57:32 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 70 -> 71
2025-06-06 00:57:32 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:57:32 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 00:57:32 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:57:32 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:57:32 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.', 'Başarılı çalışıyormu birlikte göreceğiz.']
2025-06-06 00:57:32 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.', 'Başarılı çalışıyormu birlikte göreceğiz.']
2025-06-06 00:57:37 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.285
2025-06-06 00:57:37 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.691
2025-06-06 00:57:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:57:37 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160657.wav
2025-06-06 00:57:37 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:37] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:57:37 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.360
2025-06-06 00:57:37 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.681
2025-06-06 00:57:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:57:37 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160657.wav
2025-06-06 00:57:37 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:37] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:57:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:38] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 00:57:48 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:57:48 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:57:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 70 -> 71
2025-06-06 00:57:48 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:57:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 70 -> 71
2025-06-06 00:57:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:57:48 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:57:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.', 'Başarılı çalışıyormu birlikte göreceğiz.']
2025-06-06 00:57:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:57:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım.', 'Başarılı çalışıyormu birlikte göreceğiz.']
2025-06-06 00:57:51 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.648
2025-06-06 00:57:51 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.724
2025-06-06 00:57:51 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:57:51 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160671.wav
2025-06-06 00:57:51 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:51] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:57:52 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.834
2025-06-06 00:57:52 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.718
2025-06-06 00:57:52 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 00:57:52 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160672.wav
2025-06-06 00:57:52 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:57:52] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 00:58:54 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:58:54 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 00:58:54 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (592 karakter), kısaltılıyor
2025-06-06 00:58:54 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500 -> 532
2025-06-06 00:58:54 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (592 karakter), kısaltılıyor
2025-06-06 00:58:54 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:58:54 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500 -> 532
2025-06-06 00:58:54 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:58:54 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 00:58:54 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Star Citizen'ın Yeni Yaması Alpha dört.", 'iki.', 'sıfır Star Citizen, oyunseverlerin uzun zamandır beklediği yeni alpha dört.', 'iki.', 'sıfır yaması ile birlikte önemli iyileştirmeler getiriyor.', 'Bu yama, özellikle füze sistemleri ve diğer oyun özelliklerine odaklanarak oyunun genel deneyimini geliştirmeyi hedefliyor.', 'Füze Sistemleri iyileştirildi Yeni alpha dört.', 'iki.', "sıfır yaması ile birlikte, Star Citizen'ın füze sistemleri önemli ölçüde iyileştirildi.", 'Füze kullanım deneyimi daha gerçekçi hale getirildi ve füze atış mekanikleri güncel']
2025-06-06 00:58:54 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 00:58:54 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Star Citizen'ın Yeni Yaması Alpha dört.", 'iki.', 'sıfır Star Citizen, oyunseverlerin uzun zamandır beklediği yeni alpha dört.', 'iki.', 'sıfır yaması ile birlikte önemli iyileştirmeler getiriyor.', 'Bu yama, özellikle füze sistemleri ve diğer oyun özelliklerine odaklanarak oyunun genel deneyimini geliştirmeyi hedefliyor.', 'Füze Sistemleri iyileştirildi Yeni alpha dört.', 'iki.', "sıfır yaması ile birlikte, Star Citizen'ın füze sistemleri önemli ölçüde iyileştirildi.", 'Füze kullanım deneyimi daha gerçekçi hale getirildi ve füze atış mekanikleri güncel']
2025-06-06 00:58:57 | reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-06-06 00:58:57 | reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.

2025-06-06 00:58:57 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:58:57] "[35m[1mPOST /api/tts HTTP/1.1[0m" 500 -
2025-06-06 00:58:57 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 00:58:57] "[35m[1mPOST /api/tts HTTP/1.1[0m" 500 -
2025-06-06 01:01:11 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 01:01:11 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 01:01:11 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 01:01:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:01:13] "GET / HTTP/1.1" 200 -
2025-06-06 01:01:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:01:13] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 01:01:13 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:01:13 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:01:13 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 01:01:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:01:13] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 01:01:13 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:01:14 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:01:18 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:01:18 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:01:18 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:01:18 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:01:18 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:01:18 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:01:18 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:01:18 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:01:37 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:01:37 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:01:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:01:37 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-06 01:01:37 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:01:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:01:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 01:01:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.502
2025-06-06 01:01:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.306
2025-06-06 01:01:38 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:01:38 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:01:38] "GET /api/status HTTP/1.1" 200 -
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (592 karakter), kısaltılıyor
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500 -> 500
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:01:47 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:01:47 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Star Citizen'ın Yeni Yaması Alpha 4.2.0", 'Star Citizen, oyunseverlerin uzun zamandır beklediği yeni alpha 4.2.0 yaması ile birlikte önemli iyileştirmeler getiriyor.', 'Bu yama, özellikle füze sistemleri ve diğer oyun özelliklerine odaklanarak oyunun genel deneyimini geliştirmeyi hedefliyor.', 'Füze Sistemleri İyileştirildi', "Yeni alpha 4.2.0 yaması ile birlikte, Star Citizen'ın füze sistemleri önemli ölçüde iyileştirildi.", 'Füze kullanım deneyimi daha gerçekçi hale getirildi ve füze atış mekanikleri güncel']
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (592 karakter), kısaltılıyor
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500 -> 500
2025-06-06 01:01:47 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:01:47 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:01:47 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Star Citizen'ın Yeni Yaması Alpha 4.2.0", 'Star Citizen, oyunseverlerin uzun zamandır beklediği yeni alpha 4.2.0 yaması ile birlikte önemli iyileştirmeler getiriyor.', 'Bu yama, özellikle füze sistemleri ve diğer oyun özelliklerine odaklanarak oyunun genel deneyimini geliştirmeyi hedefliyor.', 'Füze Sistemleri İyileştirildi', "Yeni alpha 4.2.0 yaması ile birlikte, Star Citizen'ın füze sistemleri önemli ölçüde iyileştirildi.", 'Füze kullanım deneyimi daha gerçekçi hale getirildi ve füze atış mekanikleri güncel']
2025-06-06 01:02:11 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 23.831
2025-06-06 01:02:11 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.561
2025-06-06 01:02:11 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:02:11 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160931.wav
2025-06-06 01:02:11 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-06 01:02:11 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:02:11] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:02:13 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 25.462
2025-06-06 01:02:13 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.534
2025-06-06 01:02:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:02:13 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749160933.wav
2025-06-06 01:02:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:02:13] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:02:13 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:02:13] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 01:03:26 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 01:03:26 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 01:03:26 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 01:03:30 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:03:30] "GET / HTTP/1.1" 200 -
2025-06-06 01:03:30 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:03:30] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 01:03:31 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:03:31 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:03:31 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 01:03:31 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:03:31] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 01:03:31 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:03:31 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:03:36 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:03:36 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:03:36 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:03:36 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:03:36 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:03:36 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:03:37 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:03:37 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:03:53 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:03:53 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:03:53 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:03:53 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-06 01:03:53 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:03:53 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:03:53 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 01:03:54 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.539
2025-06-06 01:03:54 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.226
2025-06-06 01:03:54 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:03:54 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:03:54] "GET /api/status HTTP/1.1" 200 -
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (592 karakter), kısaltılıyor
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500 -> 499
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:04:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:04:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Star Citizen'ın Yeni Yaması Alpha 4.2.0 Star Citizen, oyunseverlerin uzun zamandır beklediği yeni alpha 4.2.0 yaması ile birlikte önemli iyileştirmeler getiriyor.", 'Bu yama, özellikle füze sistemleri ve diğer oyun özelliklerine odaklanarak oyunun genel deneyimini geliştirmeyi hedefliyor.', "Füze Sistemleri iyileştirildi Yeni alpha 4.2.0 yaması ile birlikte, Star Citizen'ın füze sistemleri önemli ölçüde iyileştirildi.", 'Füze kullanım deneyimi daha gerçekçi hale getirildi ve füze atış mekanikleri güncel']
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [33mWARNING[0m | Metin çok uzun (592 karakter), kısaltılıyor
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 500 -> 499
2025-06-06 01:04:03 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:04:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:04:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Star Citizen'ın Yeni Yaması Alpha 4.2.0 Star Citizen, oyunseverlerin uzun zamandır beklediği yeni alpha 4.2.0 yaması ile birlikte önemli iyileştirmeler getiriyor.", 'Bu yama, özellikle füze sistemleri ve diğer oyun özelliklerine odaklanarak oyunun genel deneyimini geliştirmeyi hedefliyor.', "Füze Sistemleri iyileştirildi Yeni alpha 4.2.0 yaması ile birlikte, Star Citizen'ın füze sistemleri önemli ölçüde iyileştirildi.", 'Füze kullanım deneyimi daha gerçekçi hale getirildi ve füze atış mekanikleri güncel']
2025-06-06 01:04:24 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 21.400
2025-06-06 01:04:24 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.547
2025-06-06 01:04:24 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:04:25 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161064.wav
2025-06-06 01:04:25 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:04:25] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:04:25 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 22.335
2025-06-06 01:04:25 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.542
2025-06-06 01:04:25 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:04:25 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161065.wav
2025-06-06 01:04:25 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:04:25] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:04:28 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:04:28] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 01:05:20 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:05:20 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:05:20 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:05:20 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:05:20 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:05:20 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:05:20 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir.', 'Virgüller, noktalar ve diğer noktalama işaretleri var.', 'Sistem bunları nasıl işliyor?', 'Çok merak ediyorum!', 'Umarım daha doğal konuşur.']
2025-06-06 01:05:20 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir.', 'Virgüller, noktalar ve diğer noktalama işaretleri var.', 'Sistem bunları nasıl işliyor?', 'Çok merak ediyorum!', 'Umarım daha doğal konuşur.']
2025-06-06 01:05:29 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 9.282
2025-06-06 01:05:29 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.667
2025-06-06 01:05:29 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:05:29 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161129.wav
2025-06-06 01:05:29 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:05:29] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:05:30 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 10.098
2025-06-06 01:05:30 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.648
2025-06-06 01:05:30 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:05:30 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161130.wav
2025-06-06 01:05:30 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:05:30] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:05:52 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 01:05:52 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 01:05:52 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:05:52 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:05:52 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 01:05:52 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 01:05:52 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:05:52 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:05:52 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir.', 'Virgüller, noktalar ve diğer noktalama işaretleri var.', 'Sistem bunları nasıl işliyor?', 'Çok merak ediyorum!', 'Umarım daha doğal konuşur.']
2025-06-06 01:05:52 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir.', 'Virgüller, noktalar ve diğer noktalama işaretleri var.', 'Sistem bunları nasıl işliyor?', 'Çok merak ediyorum!', 'Umarım daha doğal konuşur.']
2025-06-06 01:06:01 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 8.808
2025-06-06 01:06:01 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.675
2025-06-06 01:06:01 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:06:01 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161161.wav
2025-06-06 01:06:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:06:01] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:06:01 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 8.892
2025-06-06 01:06:01 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.676
2025-06-06 01:06:01 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:06:01 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161161.wav
2025-06-06 01:06:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:06:01] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:10:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:10:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:10:48 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:10:48 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:10:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:10:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:10:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir.', 'Virgüller, noktalar ve diğer noktalama işaretleri var.', 'Sistem bunları nasıl işliyor?', 'Çok merak ediyorum!', 'Umarım daha doğal konuşur.']
2025-06-06 01:10:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir.', 'Virgüller, noktalar ve diğer noktalama işaretleri var.', 'Sistem bunları nasıl işliyor?', 'Çok merak ediyorum!', 'Umarım daha doğal konuşur.']
2025-06-06 01:10:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 8.846
2025-06-06 01:10:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.641
2025-06-06 01:10:57 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:10:57 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161457.wav
2025-06-06 01:10:57 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:10:57] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:10:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 9.029
2025-06-06 01:10:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.633
2025-06-06 01:10:57 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:10:57 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749161457.wav
2025-06-06 01:10:57 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:10:57] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:24:00 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-06 01:24:00 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-06 01:24:00 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-06 01:24:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:24:14] "GET / HTTP/1.1" 200 -
2025-06-06 01:24:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:24:14] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-06 01:24:14 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-06 01:24:14 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-06 01:24:14 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-06 01:24:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:24:14] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 01:24:14 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-06 01:24:15 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-06 01:24:25 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-06 01:24:25 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-06 01:24:25 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-06 01:24:25 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-06 01:24:25 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-06 01:24:25 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:24:25 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-06 01:24:26 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.299
2025-06-06 01:24:26 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.075
2025-06-06 01:24:26 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:24:26 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:24:26] "GET /api/status HTTP/1.1" 200 -
2025-06-06 01:24:29 | reclasttts.core.voice_cloner | [32mINFO[0m | Minimal işleme tamamlandı: processed_1.wav
2025-06-06 01:24:29 | reclasttts.core.voice_cloner | [32mINFO[0m | Referans dosyası işlendi: reference_1.wav
2025-06-06 01:24:29 | reclasttts.core.voice_cloner | [32mINFO[0m | ✅ Ses klonu oluşturuldu: Umit2
2025-06-06 01:24:29 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:24:29] "POST /api/voices/create HTTP/1.1" 200 -
2025-06-06 01:24:29 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:24:29] "GET /api/voices HTTP/1.1" 200 -
2025-06-06 01:25:05 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 01:25:05 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 01:25:05 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:25:05 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 160 -> 160
2025-06-06 01:25:05 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 01:25:05 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 01:25:05 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:25:05 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:25:05 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir  Virgüller, noktalar ve diğer noktalama işaretleri var  Sistem bunları nasıl işliyor  Çok merak ediyorum  Umarım daha doğal konuşur']
2025-06-06 01:25:05 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu uzun bir metin örneğidir  Virgüller, noktalar ve diğer noktalama işaretleri var  Sistem bunları nasıl işliyor  Çok merak ediyorum  Umarım daha doğal konuşur']
2025-06-06 01:25:11 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 6.165
2025-06-06 01:25:11 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.675
2025-06-06 01:25:11 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:25:11 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749162311.wav
2025-06-06 01:25:11 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:25:11] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:25:12 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 6.616
2025-06-06 01:25:12 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.668
2025-06-06 01:25:12 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:25:12 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749162312.wav
2025-06-06 01:25:12 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:25:12] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:25:16 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:25:16] "[36mGET /assets/logo.ico HTTP/1.1[0m" 304 -
2025-06-06 01:25:41 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 01:25:41 | reclasttts.core.voice_cloner | [32mINFO[0m | Ses klonu dosyaları alındı (orijinal): 1 dosya
2025-06-06 01:25:41 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 138 -> 138
2025-06-06 01:25:41 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 138 -> 138
2025-06-06 01:25:41 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 01:25:41 | reclasttts.core.tts_engine | [32mINFO[0m | Ses klonlama kullanılıyor: 1 dosya
2025-06-06 01:25:41 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:25:41 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-06 01:25:41 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba  Ben çok mutluyum bugün  Bu harika bir gün, değil mi  Umarım sen de mutlusundur  Hayat bazen zor olabilir, ama güzel anlar da var']
2025-06-06 01:25:41 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba  Ben çok mutluyum bugün  Bu harika bir gün, değil mi  Umarım sen de mutlusundur  Hayat bazen zor olabilir, ama güzel anlar da var']
2025-06-06 01:25:48 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 7.328
2025-06-06 01:25:48 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.756
2025-06-06 01:25:48 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:25:48 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749162348.wav
2025-06-06 01:25:48 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:25:48] "POST /api/tts HTTP/1.1" 200 -
2025-06-06 01:25:49 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 8.226
2025-06-06 01:25:49 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.708
2025-06-06 01:25:49 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-06 01:25:49 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749162349.wav
2025-06-06 01:25:49 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [06/Jun/2025 01:25:49] "POST /api/tts HTTP/1.1" 200 -
2025-06-07 22:46:48 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-07 22:46:48 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-07 22:46:48 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-07 22:46:51 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:46:51] "GET / HTTP/1.1" 200 -
2025-06-07 22:46:51 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:46:51] "GET /assets/logo.png HTTP/1.1" 200 -
2025-06-07 22:46:51 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 22:46:51 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 22:46:51 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-07 22:46:51 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:46:51] "GET /api/voices HTTP/1.1" 200 -
2025-06-07 22:46:51 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 22:46:52 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 22:47:03 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 22:47:03 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 22:47:03 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 22:47:03 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-07 22:47:03 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 22:47:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 22:47:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-07 22:47:05 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.004
2025-06-07 22:47:05 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.079
2025-06-07 22:47:05 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 22:47:05 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:47:05] "GET /api/status HTTP/1.1" 200 -
2025-06-07 22:47:12 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29 -> 29
2025-06-07 22:47:12 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29 -> 29
2025-06-07 22:47:12 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 22:47:12 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 22:47:12 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 22:47:12 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 22:47:12 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım']
2025-06-07 22:47:12 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, Test edelim bakalım']
2025-06-07 22:47:14 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.893
2025-06-07 22:47:14 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.738
2025-06-07 22:47:14 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 22:47:14 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749325634.wav
2025-06-07 22:47:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:47:14] "POST /api/tts HTTP/1.1" 200 -
2025-06-07 22:47:14 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.939
2025-06-07 22:47:14 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.713
2025-06-07 22:47:14 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 22:47:14 | reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: C:\Users\<USER>\Desktop\RecLastTTS\output\tts_output_1749325634.wav
2025-06-07 22:47:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:47:14] "POST /api/tts HTTP/1.1" 200 -
2025-06-07 22:47:16 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 22:47:16] "GET /assets/logo.ico HTTP/1.1" 200 -
2025-06-07 23:14:21 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 10 parçaya bölündü (toplam 200 kelime)
2025-06-07 23:14:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:14:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:14:21 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:14:22 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:14:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:14:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:14:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:14:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 85 -> 177
2025-06-07 23:14:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:32 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:32 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba! haha Bu çok komik bir durum.', 'Gerçekten heyecan verici!']
2025-06-07 23:14:35 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.702
2025-06-07 23:14:35 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.409
2025-06-07 23:14:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_1.wav
2025-06-07 23:14:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 87 -> 174
2025-06-07 23:14:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:35 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:35 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu korkunç bir hikaye.', '. .', 'Ama sonunda mutlu son var!']
2025-06-07 23:14:36 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.806
2025-06-07 23:14:36 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.294
2025-06-07 23:14:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_2.wav
2025-06-07 23:14:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 85 -> 195
2025-06-07 23:14:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:36 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:36 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.', '. .', 'Ama şimdi herkese söylüyorum!']
2025-06-07 23:14:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.804
2025-06-07 23:14:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.286
2025-06-07 23:14:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_3.wav
2025-06-07 23:14:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 62 -> 80
2025-06-07 23:14:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:38 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:38 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma.', 'Uzun bir bekleme sonrası devam ediyoruz.']
2025-06-07 23:14:40 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.700
2025-06-07 23:14:40 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.290
2025-06-07 23:14:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_4.wav
2025-06-07 23:14:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-07 23:14:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:14:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:14:40 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:14:40 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:14:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:14:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:14:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:14:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 52 -> 53
2025-06-07 23:14:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:50 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:50 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, nasılsınız?', 'Bugün hava çok güzel, değil mi?']
2025-06-07 23:14:52 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.634
2025-06-07 23:14:52 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.334
2025-06-07 23:14:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_1.wav
2025-06-07 23:14:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 65 -> 66
2025-06-07 23:14:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:52 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:52 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Türkçe karakterler: ğüşıöç.', 'Bu karakterlerin telaffuzu önemlidir.']
2025-06-07 23:14:54 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.987
2025-06-07 23:14:54 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.294
2025-06-07 23:14:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_2.wav
2025-06-07 23:14:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 75 -> 94
2025-06-07 23:14:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:54 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:54 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Doktor Ahmet, Profesör Mehmet ile ve saire konuları konuştu.', 'Örn.', 'matematik, fizik ve benzeri']
2025-06-07 23:14:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.832
2025-06-07 23:14:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.297
2025-06-07 23:14:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:14:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_3.wav
2025-06-07 23:14:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 60 -> 66
2025-06-07 23:14:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:14:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:14:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sayılar: bir.', 'madde, iki.', 'paragraf, üç.', 'bölüm şeklinde sıralanır.']
2025-06-07 23:15:00 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.585
2025-06-07 23:15:00 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.288
2025-06-07 23:15:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:15:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_4.wav
2025-06-07 23:15:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 66 -> 75
2025-06-07 23:15:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:15:00 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:15:00 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['internet adresleri: www nokta example.', 'com, test.', 'tr gibi örnekler vardır.']
2025-06-07 23:15:02 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.782
2025-06-07 23:15:02 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.300
2025-06-07 23:15:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:15:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_5.wav
2025-06-07 23:15:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-07 23:15:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:15:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:15:02 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:15:03 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (330 kelime), chunk'lara bölünüyor...
2025-06-07 23:15:13 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 4 parçaya bölündü (toplam 330 kelime)
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 4 chunk'a bölündü
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/4 işleniyor...
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 829 -> 990
2025-06-07 23:15:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:15:13 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:15:13 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir.", "Başkenti Ankara, en büyük şehri ise istanbul'dur.", "Türkiye'nin tarihi çok zengindir!", 'Anadolu toprakları, tarih boyunca birçok medeniyete ev sahipliği yapmıştır.', 'Hitit, Frig, Lidya, Pers, Makedon, Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.', 'Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.', 'Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.', 'Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.', 'Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır.', 'Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.']
2025-06-07 23:15:33 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 19.605
2025-06-07 23:15:33 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.305
2025-06-07 23:15:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:15:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/4 işleniyor...
2025-06-07 23:15:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 753 -> 941
2025-06-07 23:15:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:15:33 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:15:33 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.', 'haha Kebap, baklava, Turkish delight gibi lezzetler dünya çapında tanınmaktadır.', "Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir.", "Başkenti Ankara, en büyük şehri ise istanbul'dur.", "Türkiye'nin tarihi çok zengindir!", 'Anadolu toprakları, tarih boyunca birçok medeniyete ev sahipliği yapmıştır.', 'Hitit, Frig, Lidya, Pers, Makedon, Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.', 'Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.', 'Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.']
2025-06-07 23:15:51 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 17.937
2025-06-07 23:15:51 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.303
2025-06-07 23:15:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:15:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 3/4 işleniyor...
2025-06-07 23:15:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 803 -> 979
2025-06-07 23:15:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:15:51 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:15:51 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.', 'Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.', 'Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır.', 'Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.', 'haha Kebap, baklava, Turkish delight gibi lezzetler dünya çapında tanınmaktadır.', "Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir.", "Başkenti Ankara, en büyük şehri ise istanbul'dur.", "Türkiye'nin tarihi çok zengindir!", 'Anadolu toprakları, tarih boyunca birçok medeniyete ev sahipliği yapmıştır.', 'Hitit, Frig, Lidya, Pers, Makedon, Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.']
2025-06-07 23:16:11 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 20.293
2025-06-07 23:16:11 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.306
2025-06-07 23:16:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:16:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 4/4 işleniyor...
2025-06-07 23:16:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 595 -> 757
2025-06-07 23:16:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:16:11 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:16:11 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.', 'Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.', 'Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.', 'Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.', 'Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır.', 'Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.', 'haha Kebap, baklava, Turkish delight gibi lezzetler dünya çapında tanınmaktadır.']
2025-06-07 23:16:26 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 14.649
2025-06-07 23:16:26 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.316
2025-06-07 23:16:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:16:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 5230789 sample
2025-06-07 23:16:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/long_text_test.wav
2025-06-07 23:16:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-07 23:16:56 | reclasttts.ui.app | [32mINFO[0m | 🌐 UI başlatılıyor: http://0.0.0.0:7860
2025-06-07 23:16:56 | werkzeug | [32mINFO[0m | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:7860
 * Running on http://************:7860
2025-06-07 23:16:56 | werkzeug | [32mINFO[0m | [33mPress CTRL+C to quit[0m
2025-06-07 23:17:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:01] "GET / HTTP/1.1" 200 -
2025-06-07 23:17:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:01] "GET /assets/logo.png HTTP/1.1" 200 -
2025-06-07 23:17:01 | reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:17:01 | reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:17:01 | reclasttts.core.voice_cloner | [32mINFO[0m | VoiceCloner başlatıldı
2025-06-07 23:17:01 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:01] "GET /api/voices HTTP/1.1" 200 -
2025-06-07 23:17:01 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:17:02 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:17:13 | reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:17:13 | reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:17:13 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:17:13 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-07 23:17:13 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:17:13 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:17:13 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-07 23:17:14 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.509
2025-06-07 23:17:14 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.203
2025-06-07 23:17:14 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:17:14 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:14] "GET /api/status HTTP/1.1" 200 -
2025-06-07 23:17:19 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:19] "GET / HTTP/1.1" 200 -
2025-06-07 23:17:20 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:20] "[36mGET /assets/logo.png HTTP/1.1[0m" 304 -
2025-06-07 23:17:20 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:20] "GET /api/voices HTTP/1.1" 200 -
2025-06-07 23:17:20 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 4 -> 4
2025-06-07 23:17:20 | reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:17:20 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:17:20 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test']
2025-06-07 23:17:20 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.698
2025-06-07 23:17:20 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.556
2025-06-07 23:17:20 | reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:17:20 | werkzeug | [32mINFO[0m | 127.0.0.1 - - [07/Jun/2025 23:17:20] "GET /api/status HTTP/1.1" 200 -
2025-06-07 23:56:52 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 10 parçaya bölündü (toplam 200 kelime)
2025-06-07 23:56:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:56:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:56:52 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:56:53 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:57:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:57:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:57:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:57:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 85 -> 156
2025-06-07 23:57:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba! haha Bu çok komik bir durum.', 'Gerçekten heyecan verici!']
2025-06-07 23:57:07 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.613
2025-06-07 23:57:07 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.583
2025-06-07 23:57:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_1.wav
2025-06-07 23:57:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 87 -> 151
2025-06-07 23:57:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:07 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:07 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu korkunç bir hikaye.', '. .', 'Ama sonunda mutlu son var!']
2025-06-07 23:57:10 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.087
2025-06-07 23:57:10 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.387
2025-06-07 23:57:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_2.wav
2025-06-07 23:57:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 85 -> 173
2025-06-07 23:57:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:10 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:10 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.', '. .', 'Ama şimdi herkese söylüyorum!']
2025-06-07 23:57:12 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.349
2025-06-07 23:57:12 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.391
2025-06-07 23:57:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_3.wav
2025-06-07 23:57:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 62 -> 56
2025-06-07 23:57:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:12 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:12 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma.', 'Uzun bir bekleme sonrası devam ediyoruz.']
2025-06-07 23:57:15 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.878
2025-06-07 23:57:15 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.475
2025-06-07 23:57:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_test_4.wav
2025-06-07 23:57:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-07 23:57:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:57:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:57:15 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:57:16 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:57:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:57:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:57:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:57:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 52 -> 52
2025-06-07 23:57:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:26 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:26 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba, nasılsınız?', 'Bugün hava çok güzel, değil mi?']
2025-06-07 23:57:27 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.792
2025-06-07 23:57:27 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.386
2025-06-07 23:57:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_1.wav
2025-06-07 23:57:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 65 -> 65
2025-06-07 23:57:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:27 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:27 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Türkçe karakterler: ğüşıöç.', 'Bu karakterlerin telaffuzu önemlidir.']
2025-06-07 23:57:30 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.645
2025-06-07 23:57:30 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.397
2025-06-07 23:57:30 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:30 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_2.wav
2025-06-07 23:57:30 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 75 -> 96
2025-06-07 23:57:30 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:30 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:30 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Doktor Ahmet, Profesör Mehmet ile ve saire konuları konuştu.', 'Örneğin matematik, fizik ve benzeri']
2025-06-07 23:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.600
2025-06-07 23:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.444
2025-06-07 23:57:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_3.wav
2025-06-07 23:57:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 60 -> 65
2025-06-07 23:57:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:34 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sayılar: bir.', 'madde, iki.', 'paragraf, üç.', 'bölüm şeklinde sıralanır.']
2025-06-07 23:57:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.911
2025-06-07 23:57:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.422
2025-06-07 23:57:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_4.wav
2025-06-07 23:57:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 66 -> 94
2025-06-07 23:57:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:38 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:38 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['internet adresleri: w w w nokta [EN]example.', '[/EN] com, [EN]test.', '[/EN] tr gibi örnekler vardır.']
2025-06-07 23:57:42 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.249
2025-06-07 23:57:42 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.397
2025-06-07 23:57:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_5.wav
2025-06-07 23:57:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 79 -> 106
2025-06-07 23:57:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:42 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:42 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Karışık dil: [EN]Resident[/EN] [EN]Evil[/EN] serisinin yeni oyunu [EN]Requiem,[/EN] Şubat 2026'da çıkacak."]
2025-06-07 23:57:47 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.842
2025-06-07 23:57:47 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.502
2025-06-07 23:57:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_6.wav
2025-06-07 23:57:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 74 -> 94
2025-06-07 23:57:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:47 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:47 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Web siteleri: w w w nokta umiteski.', 'com.', 'tr sitemizi ziyaret edin.', '[EN]test.', '[/EN] tr de güzel.']
2025-06-07 23:57:52 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.745
2025-06-07 23:57:52 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.392
2025-06-07 23:57:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_7.wav
2025-06-07 23:57:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 63 -> 74
2025-06-07 23:57:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:52 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:52 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Noktalama: Bu bir [EN]test.', '[/EN] .', '.', 'Gerçekten mi?', 'Evet!', 'Harika, değil mi?']
2025-06-07 23:57:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.748
2025-06-07 23:57:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.418
2025-06-07 23:57:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:57:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_8.wav
2025-06-07 23:57:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 60 -> 69
2025-06-07 23:57:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:57:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:57:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Kısaltmalar düzeltmesi: Örneğin bu metin Doktor ve Profesör içeriyor.']
2025-06-07 23:58:00 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.758
2025-06-07 23:58:00 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.451
2025-06-07 23:58:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:58:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/turkish_test_9.wav
2025-06-07 23:58:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-07 23:58:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-07 23:58:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-07 23:58:00 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-07 23:58:01 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (330 kelime), chunk'lara bölünüyor...
2025-06-07 23:58:12 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 4 parçaya bölündü (toplam 330 kelime)
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 4 chunk'a bölündü
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/4 işleniyor...
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 829 -> 899
2025-06-07 23:58:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:58:12 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:58:12 | TTS.utils.synthesizer | [32mINFO[0m | Input: ["Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir.", "Başkenti Ankara, en büyük şehri ise istanbul'dur.", "Türkiye'nin tarihi çok zengindir!", 'Anadolu toprakları, tarih boyunca birçok medeniyete ev sahipliği yapmıştır.', 'Hitit, Frig, Lidya, Pers, Makedon, Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.', 'Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.', 'Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.', 'Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.', 'Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır.', 'Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.']
2025-06-07 23:58:40 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 28.848
2025-06-07 23:58:40 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.462
2025-06-07 23:58:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:58:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/4 işleniyor...
2025-06-07 23:58:41 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 753 -> 829
2025-06-07 23:58:41 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:58:41 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:58:41 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.', 'haha Kebap, baklava, Turkish delight gibi lezzetler dünya çapında tanınmaktadır.', "Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir.", "Başkenti Ankara, en büyük şehri ise istanbul'dur.", "Türkiye'nin tarihi çok zengindir!", 'Anadolu toprakları, tarih boyunca birçok medeniyete ev sahipliği yapmıştır.', 'Hitit, Frig, Lidya, Pers, Makedon, Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.', 'Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.', 'Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.']
2025-06-07 23:59:07 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 26.434
2025-06-07 23:59:07 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.452
2025-06-07 23:59:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:59:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 3/4 işleniyor...
2025-06-07 23:59:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 803 -> 889
2025-06-07 23:59:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:59:07 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:59:07 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.', 'Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.', 'Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır.', 'Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.', 'haha Kebap, baklava, Turkish delight gibi lezzetler dünya çapında tanınmaktadır.', "Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir.", "Başkenti Ankara, en büyük şehri ise istanbul'dur.", "Türkiye'nin tarihi çok zengindir!", 'Anadolu toprakları, tarih boyunca birçok medeniyete ev sahipliği yapmıştır.', 'Hitit, Frig, Lidya, Pers, Makedon, Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.']
2025-06-07 23:59:35 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 28.417
2025-06-07 23:59:35 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.450
2025-06-07 23:59:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:59:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 4/4 işleniyor...
2025-06-07 23:59:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 595 -> 690
2025-06-07 23:59:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-07 23:59:35 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-07 23:59:35 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.', 'Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.', 'Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.', 'Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.', 'Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır.', 'Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir.', 'haha Kebap, baklava, Turkish delight gibi lezzetler dünya çapında tanınmaktadır.']
2025-06-07 23:59:56 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 20.333
2025-06-07 23:59:56 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.456
2025-06-07 23:59:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-07 23:59:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 5058245 sample
2025-06-07 23:59:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/long_text_test.wav
2025-06-07 23:59:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 00:39:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 00:39:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 00:39:01 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 00:39:01 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 00:39:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 00:39:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 00:39:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 00:39:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 79 -> 49
2025-06-08 00:39:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:11 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:11 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu korkunç bir hikaye.', '<#bir#> Ama mutlu son var!']
2025-06-08 00:39:13 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.614
2025-06-08 00:39:13 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.387
2025-06-08 00:39:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/natural_emotion_1.wav
2025-06-08 00:39:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 49 -> 28
2025-06-08 00:39:13 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:13 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:13 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.', 'Kimseye söyleme.']
2025-06-08 00:39:15 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.460
2025-06-08 00:39:15 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.314
2025-06-08 00:39:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/natural_emotion_2.wav
2025-06-08 00:39:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 49 -> 30
2025-06-08 00:39:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:15 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:15 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Harika!', 'Bu muhteşem bir haber!']
2025-06-08 00:39:16 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.931
2025-06-08 00:39:16 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.272
2025-06-08 00:39:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/natural_emotion_3.wav
2025-06-08 00:39:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 48 -> 50
2025-06-08 00:39:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:16 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:16 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma <#iki#> uzun bekleme sonrası devam.']
2025-06-08 00:39:17 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.705
2025-06-08 00:39:17 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.291
2025-06-08 00:39:17 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:17 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/natural_emotion_4.wav
2025-06-08 00:39:17 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 00:39:17 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 00:39:17 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 00:39:17 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 00:39:18 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 00:39:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 00:39:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 00:39:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 00:39:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 11
2025-06-08 00:39:27 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:27 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:27 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.']
2025-06-08 00:39:28 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.621
2025-06-08 00:39:28 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.294
2025-06-08 00:39:28 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:28 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/problem_test_1.wav
2025-06-08 00:39:28 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 16
2025-06-08 00:39:28 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:28 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:28 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['1. madde önemli.']
2025-06-08 00:39:29 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.998
2025-06-08 00:39:29 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.354
2025-06-08 00:39:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/problem_test_2.wav
2025-06-08 00:39:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 22 -> 63
2025-06-08 00:39:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:29 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:29 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['dabılyu dabılyu dabılyu nokta [EN]example[/EN] nokta kom sitesi']
2025-06-08 00:39:31 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.106
2025-06-08 00:39:31 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.292
2025-06-08 00:39:31 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:31 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/problem_test_3.wav
2025-06-08 00:39:31 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 26
2025-06-08 00:39:31 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:31 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:31 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Türkçe karakterler: ğüşıöç']
2025-06-08 00:39:32 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.111
2025-06-08 00:39:32 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.315
2025-06-08 00:39:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/problem_test_4.wav
2025-06-08 00:39:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 25 -> 32
2025-06-08 00:39:32 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:32 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:32 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['[EN]Test.', '[/EN] nokta nokta nokta']
2025-06-08 00:39:34 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.396
2025-06-08 00:39:34 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.277
2025-06-08 00:39:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:39:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/problem_test_5.wav
2025-06-08 00:39:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 00:39:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 00:39:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 00:39:34 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 00:39:34 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 00:39:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 00:39:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 00:39:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 00:39:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 734 -> 655
2025-06-08 00:39:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:39:43 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:39:43 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba arkadaşlar!', 'Bugün RecLastTTS sisteminin doğal konuşma özelliklerini [EN]test[/EN] ediyoruz.', '<#bir#> Sistemin yeni özellikleri:', '1. Doğal Türkçe telaffuz', '2. İngilizce kelime tespiti: [EN]YouTube,[/EN] [EN]Resident[/EN] [EN]Evil[/EN] gibi', '3. Web adresi telaffuzu: dabılyu dabılyu dabılyu nokta [EN]example[/EN] nokta kom', '4. Kısaltma işleme: Doktor Ahmet, Profesör Mehmet, ve saire <#1.beş#> Gizli bilgi: Sistem artık çok daha doğal konuşuyor.', 'Türkçe karakterler ğüşıöç düzgün telaffuz ediliyor.', '<#0.beş#> Dikkat!', '[EN]Test[/EN] sırasında beklenmedik durumlar olabilir.', '<#bir#> Ama endişelenmeyin, her şey kontrol altında!', 'Test tamamlandı.', 'Teşekkürler.']
2025-06-08 00:40:03 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 20.018
2025-06-08 00:40:03 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.291
2025-06-08 00:40:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:40:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/natural_comprehensive.wav
2025-06-08 00:40:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 00:46:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 00:46:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 00:46:33 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 00:46:33 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 00:46:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 00:46:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 00:46:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 00:46:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 36 -> 36
2025-06-08 00:46:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:46:43 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:46:43 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['1. madde önemli.', '2. paragraf da var.']
2025-06-08 00:46:46 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.251
2025-06-08 00:46:46 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.603
2025-06-08 00:46:46 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:46:46 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_fix_1.wav
2025-06-08 00:46:46 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 11
2025-06-08 00:46:46 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:46:46 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:46:46 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.']
2025-06-08 00:46:47 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.975
2025-06-08 00:46:47 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.449
2025-06-08 00:46:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:46:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_fix_2.wav
2025-06-08 00:46:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 43 -> 39
2025-06-08 00:46:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:46:47 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:46:47 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma   bekleme sonrası devam.']
2025-06-08 00:46:48 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.337
2025-06-08 00:46:48 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.457
2025-06-08 00:46:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:46:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_fix_3.wav
2025-06-08 00:46:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29 -> 70
2025-06-08 00:46:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:46:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:46:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['dabılyu dabılyu dabılyu nokta [EN]example[/EN] nokta kom sitesi güzel.']
2025-06-08 00:46:51 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.130
2025-06-08 00:46:51 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.413
2025-06-08 00:46:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:46:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_fix_4.wav
2025-06-08 00:46:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 40 -> 49
2025-06-08 00:46:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:46:51 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:46:51 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test kelimesi Türkçe, [EN]example[/EN] İngilizce.']
2025-06-08 00:46:53 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.042
2025-06-08 00:46:53 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.409
2025-06-08 00:46:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:46:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_fix_5.wav
2025-06-08 00:46:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37 -> 37
2025-06-08 00:46:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:46:53 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:46:53 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Türkçe karakterler: ğüşıöç telaffuzu.']
2025-06-08 00:46:55 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.949
2025-06-08 00:46:55 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.482
2025-06-08 00:46:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:46:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_fix_6.wav
2025-06-08 00:46:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 00:46:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 00:46:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 00:46:55 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 00:46:56 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (55 kelime), chunk'lara bölünüyor...
2025-06-08 00:47:06 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 2 parçaya bölündü (toplam 55 kelime)
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 2 chunk'a bölündü
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/2 işleniyor...
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 324 -> 367
2025-06-08 00:47:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:47:06 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:47:06 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba!', 'RecLastTTS sisteminin son halini test ediyoruz.', 'Düzeltilen özellikler:', '1. Sıralama sayıları: birinci madde, ikinci paragraf', '2. Web adresleri: dabılyu dabılyu dabılyu nokta [EN]example[/EN] nokta kom doğru telaffuz', '3. Kısaltmalar: Doktor Ahmet, Profesör Mehmet', '4. İngilizce kelimeler: [EN]YouTube,[/EN] [EN]Resident[/EN] [EN]Evil[/EN]   Bu bir sır.']
2025-06-08 00:47:16 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 10.569
2025-06-08 00:47:16 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.295
2025-06-08 00:47:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:47:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/2 işleniyor...
2025-06-08 00:47:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 262 -> 207
2025-06-08 00:47:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 00:47:16 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 00:47:16 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['4. İngilizce kelimeler: [EN]YouTube,[/EN] [EN]Resident[/EN] [EN]Evil[/EN]   Bu bir sır.', 'Fısıltı testi.', 'Korkunç hikaye.', 'Mutlu son!', 'Türkçe karakterler: ğüşıöç düzgün telaffuz.', 'Test tamamlandı.', 'Teşekkürler.']
2025-06-08 00:47:25 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 8.610
2025-06-08 00:47:25 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.342
2025-06-08 00:47:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 00:47:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 1351095 sample
2025-06-08 00:47:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_comprehensive.wav
2025-06-08 00:47:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:00:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:00:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:00:45 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:00:46 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:00:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:00:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:00:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:00:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 11
2025-06-08 01:00:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:00:55 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:00:55 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.']
2025-06-08 01:00:56 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.257
2025-06-08 01:00:56 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.676
2025-06-08 01:00:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:00:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_fix_1.wav
2025-06-08 01:00:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29 -> 29
2025-06-08 01:00:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:00:56 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:00:56 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test tamamlandı.', 'Teşekkürler.']
2025-06-08 01:00:58 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.296
2025-06-08 01:00:58 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.289
2025-06-08 01:00:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:00:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_fix_2.wav
2025-06-08 01:00:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 30 -> 30
2025-06-08 01:00:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:00:58 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:00:58 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir test... Üç nokta testi.']
2025-06-08 01:00:59 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.056
2025-06-08 01:00:59 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.290
2025-06-08 01:00:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:00:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_fix_3.wav
2025-06-08 01:00:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 36 -> 36
2025-06-08 01:00:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:00:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:00:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['1. madde önemli.', '2. paragraf da var.']
2025-06-08 01:01:01 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.755
2025-06-08 01:01:01 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.332
2025-06-08 01:01:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_fix_4.wav
2025-06-08 01:01:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 46 -> 80
2025-06-08 01:01:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:01 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:01 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sayı 1.beş oranında.', 'Web dabılyu dabılyu dabılyu nokta example nokta kom sitesi.']
2025-06-08 01:01:05 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.019
2025-06-08 01:01:05 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.468
2025-06-08 01:01:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_fix_5.wav
2025-06-08 01:01:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37 -> 43
2025-06-08 01:01:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:05 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:05 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Doktor Ahmet geldi.', 'Profesör Mehmet de var.']
2025-06-08 01:01:07 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.392
2025-06-08 01:01:07 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.465
2025-06-08 01:01:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_fix_6.wav
2025-06-08 01:01:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:01:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:01:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:01:07 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:01:07 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:01:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:01:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:01:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:01:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 26
2025-06-08 01:01:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:18 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:18 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['YouTube videosunu izledim.']
2025-06-08 01:01:20 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.566
2025-06-08 01:01:20 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.444
2025-06-08 01:01:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/english_fix_1.wav
2025-06-08 01:01:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 26
2025-06-08 01:01:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:20 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:20 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Resident Evil oyunu güzel.']
2025-06-08 01:01:21 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.292
2025-06-08 01:01:21 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.492
2025-06-08 01:01:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/english_fix_2.wav
2025-06-08 01:01:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 55
2025-06-08 01:01:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['dabılyu dabılyu dabılyu nokta example nokta kom sitesi.']
2025-06-08 01:01:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.222
2025-06-08 01:01:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.445
2025-06-08 01:01:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/english_fix_3.wav
2025-06-08 01:01:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 40 -> 40
2025-06-08 01:01:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:23 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:23 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test kelimesi Türkçe, example İngilizce.']
2025-06-08 01:01:26 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.200
2025-06-08 01:01:26 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.507
2025-06-08 01:01:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/english_fix_4.wav
2025-06-08 01:01:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:01:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:01:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:01:26 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:01:26 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:01:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:01:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:01:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:01:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 49 -> 30
2025-06-08 01:01:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır...', 'Kimseye söyleme.']
2025-06-08 01:01:39 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.202
2025-06-08 01:01:39 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.460
2025-06-08 01:01:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_fix_1.wav
2025-06-08 01:01:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 48 -> 74
2025-06-08 01:01:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:39 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:39 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma  [PAUSE_MEDIUM]  bekleme  [PAUSE_EXTRA_LONG]  uzun bekleme.']
2025-06-08 01:01:42 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.783
2025-06-08 01:01:42 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.415
2025-06-08 01:01:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_fix_2.wav
2025-06-08 01:01:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 60 -> 34
2025-06-08 01:01:42 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:42 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:42 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Harika!', '<#0 nokta 5#> Sakin devam.']
2025-06-08 01:01:45 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 3.220
2025-06-08 01:01:45 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.463
2025-06-08 01:01:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:01:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/emotion_fix_3.wav
2025-06-08 01:01:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:01:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:01:45 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:01:45 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:01:46 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (66 kelime), chunk'lara bölünüyor...
2025-06-08 01:01:57 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 2 parçaya bölündü (toplam 66 kelime)
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 2 chunk'a bölündü
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/2 işleniyor...
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 352 -> 384
2025-06-08 01:01:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:01:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:01:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba!', 'RecLastTTS sisteminin düzeltilmiş halini test ediyoruz.', '[PAUSE_MEDIUM]  Düzeltilen sorunlar:', '1. Noktalama işleme: Bu bir test.', 'Cümle sonu normal.', '2. İngilizce kelimeler: YouTube, Resident Evil normal telaffuz.', '3. Web adresleri: dabılyu dabılyu dabılyu nokta example nokta kom doğru telaffuz.', '4. Ondalık sayılar: 1.beş oranında artış var.', '<#1 nokta 5#> Bu bir sır...']
2025-06-08 01:02:09 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 12.375
2025-06-08 01:02:09 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.324
2025-06-08 01:02:09 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:02:09 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/2 işleniyor...
2025-06-08 01:02:09 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 290 -> 263
2025-06-08 01:02:09 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:02:09 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:02:09 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Ondalık sayılar: 1.beş oranında artış var.', '<#1 nokta 5#> Bu bir sır...', 'Fısıltı testi yapıyoruz.', '<#0 nokta 5#> Korkunç hikaye anlatılıyor...  [PAUSE_EXTRA_LONG]  Mutlu son geldi!', 'Doktor Ahmet ve Profesör Mehmet projeyi tamamladı.', 'Teşekkürler.', 'Test bitti.', 'Başarılı.']
2025-06-08 01:02:19 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 9.711
2025-06-08 01:02:19 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.291
2025-06-08 01:02:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:02:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 1584615 sample
2025-06-08 01:02:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/comprehensive_fixes.wav
2025-06-08 01:02:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:16:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:16:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:16:49 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:16:50 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:17:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:17:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:17:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:17:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 10
2025-06-08 01:17:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:00 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:00 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır']
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.329
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 1.059
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/critical_punctuation_1.wav
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 20 -> 22
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Profesör Mehmet de var']
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.639
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.302
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/critical_punctuation_2.wav
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 24
2025-06-08 01:17:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:02 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test tamamlandı Başarılı']
2025-06-08 01:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.646
2025-06-08 01:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:17:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/critical_punctuation_3.wav
2025-06-08 01:17:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 36 -> 32
2025-06-08 01:17:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['1 madde önemli 2 paragraf da var']
2025-06-08 01:17:04 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.032
2025-06-08 01:17:04 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.292
2025-06-08 01:17:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/critical_punctuation_4.wav
2025-06-08 01:17:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 40 -> 42
2025-06-08 01:17:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:04 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:04 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran 1.beş artmış Başka 2.beş oranı da var']
2025-06-08 01:17:05 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.331
2025-06-08 01:17:05 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.303
2025-06-08 01:17:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/critical_punctuation_5.wav
2025-06-08 01:17:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 30 -> 28
2025-06-08 01:17:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:05 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:05 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir test.', '.', 'Üç nokta testi']
2025-06-08 01:17:07 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.837
2025-06-08 01:17:07 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.299
2025-06-08 01:17:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/critical_punctuation_6.wav
2025-06-08 01:17:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:17:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:17:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:17:07 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:17:08 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:17:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:17:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:17:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:17:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 43 -> 37
2025-06-08 01:17:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:18 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:18 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma  bekleme sonrası devam']
2025-06-08 01:17:19 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.937
2025-06-08 01:17:19 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.305
2025-06-08 01:17:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/silence_test_1.wav
2025-06-08 01:17:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 54 -> 52
2025-06-08 01:17:19 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:19 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:19 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Başlangıç  uzun bekleme <#0 buçuk#> kısa bekleme son']
2025-06-08 01:17:21 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.130
2025-06-08 01:17:21 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.322
2025-06-08 01:17:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/silence_test_2.wav
2025-06-08 01:17:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 53 -> 33
2025-06-08 01:17:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Heyecanlı başlangıç!', 'Normal devam']
2025-06-08 01:17:22 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.034
2025-06-08 01:17:22 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.274
2025-06-08 01:17:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/silence_test_3.wav
2025-06-08 01:17:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 62 -> 25
2025-06-08 01:17:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:22 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:22 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır.', 'Sakin devam.']
2025-06-08 01:17:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.389
2025-06-08 01:17:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.293
2025-06-08 01:17:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/silence_test_4.wav
2025-06-08 01:17:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 18
2025-06-08 01:17:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:23 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:23 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Gülme efekti test']
2025-06-08 01:17:24 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.660
2025-06-08 01:17:24 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.284
2025-06-08 01:17:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/silence_test_5.wav
2025-06-08 01:17:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:17:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:17:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:17:24 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:17:25 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:17:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:17:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:17:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:17:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 17
2025-06-08 01:17:34 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:34 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:34 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran 1.beş artmış']
2025-06-08 01:17:35 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.852
2025-06-08 01:17:35 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.360
2025-06-08 01:17:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_test_1.wav
2025-06-08 01:17:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 28 -> 29
2025-06-08 01:17:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:35 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:35 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Değer 2.beş olarak hesaplandı']
2025-06-08 01:17:36 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.448
2025-06-08 01:17:36 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.487
2025-06-08 01:17:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_test_2.wav
2025-06-08 01:17:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 17 -> 20
2025-06-08 01:17:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:36 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:36 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sonuç 3 çeyrek çıktı']
2025-06-08 01:17:37 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.950
2025-06-08 01:17:37 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.393
2025-06-08 01:17:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_test_3.wav
2025-06-08 01:17:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 29
2025-06-08 01:17:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Miktar 4 üç çeyrek belirlendi']
2025-06-08 01:17:39 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.328
2025-06-08 01:17:39 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.454
2025-06-08 01:17:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_test_4.wav
2025-06-08 01:17:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 30
2025-06-08 01:17:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:39 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:39 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Değer 1.iki ve 3.yedi arasında']
2025-06-08 01:17:40 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.500
2025-06-08 01:17:40 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.472
2025-06-08 01:17:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_test_5.wav
2025-06-08 01:17:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:17:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:17:40 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:17:40 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:17:41 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (55 kelime), chunk'lara bölünüyor...
2025-06-08 01:17:51 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 2 parçaya bölündü (toplam 55 kelime)
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 2 chunk'a bölündü
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/2 işleniyor...
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 254 -> 229
2025-06-08 01:17:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:51 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:51 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba!', 'Bu kritik test metnidir  Noktalama testleri: 1 Bu bir sır 2 Profesör Mehmet de var 3 Test tamamlandı <#1 buçuk#> Ondalık sayılar: - Oran 1.beş artmış - Değer 2.beş hesaplandı - Sonuç 3 çeyrek çıktı  Bu bir sır.', '.']
2025-06-08 01:17:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.712
2025-06-08 01:17:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.305
2025-06-08 01:17:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:17:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/2 işleniyor...
2025-06-08 01:17:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 235 -> 161
2025-06-08 01:17:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:17:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:17:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['2.beş hesaplandı - Sonuç 3 çeyrek çıktı  Bu bir sır.', '.', 'Fısıltı testi.', '<#0 buçuk#> Korkunç hikaye anlatılıyor.', 'Mutlu son geldi!', 'Gülme efekti Test bitti Başarılı.']
2025-06-08 01:18:03 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.691
2025-06-08 01:18:03 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.284
2025-06-08 01:18:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:18:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 860775 sample
2025-06-08 01:18:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/comprehensive_critical.wav
2025-06-08 01:18:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:21:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:21:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:21:43 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:21:43 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:21:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:21:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:21:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:21:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 10
2025-06-08 01:21:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:53 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:53 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır']
2025-06-08 01:21:54 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.262
2025-06-08 01:21:54 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.929
2025-06-08 01:21:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_1.wav
2025-06-08 01:21:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 17
2025-06-08 01:21:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:54 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:54 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran 1.beş artmış']
2025-06-08 01:21:55 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.820
2025-06-08 01:21:55 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.291
2025-06-08 01:21:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_2.wav
2025-06-08 01:21:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 21 -> 22
2025-06-08 01:21:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:55 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:55 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Değer 2.beş hesaplandı']
2025-06-08 01:21:56 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.742
2025-06-08 01:21:56 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.283
2025-06-08 01:21:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_3.wav
2025-06-08 01:21:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 17 -> 21
2025-06-08 01:21:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:56 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:56 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sonuç üç çeyrek çıktı']
2025-06-08 01:21:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.697
2025-06-08 01:21:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.282
2025-06-08 01:21:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_4.wav
2025-06-08 01:21:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37 -> 31
2025-06-08 01:21:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma  bekleme sonrası']
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.134
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.288
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_5.wav
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 31 -> 23
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Başlangıç  kısa bekleme']
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.633
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_6.wav
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 41 -> 21
2025-06-08 01:21:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:58 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Heyecan!', 'Normal devam']
2025-06-08 01:21:59 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.878
2025-06-08 01:21:59 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.291
2025-06-08 01:21:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:21:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_7.wav
2025-06-08 01:21:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 24
2025-06-08 01:21:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:21:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:21:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test tamamlandı Başarılı']
2025-06-08 01:22:00 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.673
2025-06-08 01:22:00 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.284
2025-06-08 01:22:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:22:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_critical_8.wav
2025-06-08 01:22:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:22:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:22:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:22:00 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:22:00 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (54 kelime), chunk'lara bölünüyor...
2025-06-08 01:22:10 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 2 parçaya bölündü (toplam 54 kelime)
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 2 chunk'a bölündü
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/2 işleniyor...
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 248 -> 228
2025-06-08 01:22:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:22:10 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:22:10 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba!', 'Bu son test metnidir  Noktalama testleri: 1 Bu bir sır 2 Test tamamlandı 3 Profesör Mehmet de var  Ondalık sayılar: - Oran 1.beş artmış - Değer 2.beş hesaplandı - Sonuç üç çeyrek çıktı - Miktar dört üç çeyrek belirlendi']
2025-06-08 01:22:15 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.460
2025-06-08 01:22:15 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.316
2025-06-08 01:22:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:22:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/2 işleniyor...
2025-06-08 01:22:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 221 -> 154
2025-06-08 01:22:15 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:22:15 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:22:15 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['2.beş hesaplandı - Sonuç üç çeyrek çıktı - Miktar dört üç çeyrek belirlendi  Bu bir sır.', '.', 'Fısıltı testi.', 'Korkunç hikaye.', 'Mutlu son!', 'Test bitti Başarılı.']
2025-06-08 01:22:21 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.458
2025-06-08 01:22:21 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.296
2025-06-08 01:22:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:22:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 793943 sample
2025-06-08 01:22:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_comprehensive.wav
2025-06-08 01:22:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:24:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:24:47 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:24:47 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:24:48 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:24:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:24:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:24:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:24:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 10
2025-06-08 01:24:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:24:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:24:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır']
2025-06-08 01:24:58 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.240
2025-06-08 01:24:58 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.768
2025-06-08 01:24:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:24:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_1.wav
2025-06-08 01:24:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 17
2025-06-08 01:24:58 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:24:58 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:24:58 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran 1.beş artmış']
2025-06-08 01:24:59 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.774
2025-06-08 01:24:59 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:24:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:24:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_2.wav
2025-06-08 01:24:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 21 -> 22
2025-06-08 01:24:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:24:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:24:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Değer 2.beş hesaplandı']
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.745
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.290
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_3.wav
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 17 -> 21
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sonuç üç çeyrek çıktı']
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.688
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.304
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_4.wav
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 32
2025-06-08 01:25:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:00 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Miktar dört üç çeyrek belirlendi']
2025-06-08 01:25:01 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.912
2025-06-08 01:25:01 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.307
2025-06-08 01:25:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_5.wav
2025-06-08 01:25:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37 -> 31
2025-06-08 01:25:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:01 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:01 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma  bekleme sonrası']
2025-06-08 01:25:02 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.811
2025-06-08 01:25:02 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.298
2025-06-08 01:25:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_6.wav
2025-06-08 01:25:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 31 -> 23
2025-06-08 01:25:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:02 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:02 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Başlangıç  kısa bekleme']
2025-06-08 01:25:03 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.648
2025-06-08 01:25:03 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.292
2025-06-08 01:25:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_7.wav
2025-06-08 01:25:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 41 -> 21
2025-06-08 01:25:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Heyecan!', 'Normal devam']
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.842
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.294
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_8.wav
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 24
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test tamamlandı Başarılı']
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.688
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.296
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_9.wav
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29 -> 60
2025-06-08 01:25:04 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:04 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['dabılyu dabılyu dabılyu nokta example nokta kom sitesi güzel']
2025-06-08 01:25:06 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.460
2025-06-08 01:25:06 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.295
2025-06-08 01:25:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/perfect_test_10.wav
2025-06-08 01:25:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:25:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:25:06 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:25:06 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:25:06 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (75 kelime), chunk'lara bölünüyor...
2025-06-08 01:25:16 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 3 parçaya bölündü (toplam 75 kelime)
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 3 chunk'a bölündü
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/3 işleniyor...
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 264 -> 235
2025-06-08 01:25:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:16 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:16 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba arkadaşlar!', 'RecLastTTS sisteminin mükemmel halini test ediyoruz  Noktalama testleri: 1 Bu bir sır 2 Test tamamlandı 3 Profesör Mehmet de var  Ondalık sayılar: - Oran 1.beş artmış - Değer 2.beş hesaplandı - Sonuç üç çeyrek çıktı']
2025-06-08 01:25:21 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.211
2025-06-08 01:25:21 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.332
2025-06-08 01:25:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/3 işleniyor...
2025-06-08 01:25:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 173 -> 218
2025-06-08 01:25:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['1.beş artmış - Değer 2.beş hesaplandı - Sonuç üç çeyrek çıktı - Miktar dört üç çeyrek belirlendi  Web adresleri: - dabılyu dabılyu dabılyu nokta example nokta kom sitesi - test nokta te er adresi  Bu bir sır.', '.']
2025-06-08 01:25:26 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.344
2025-06-08 01:25:26 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.299
2025-06-08 01:25:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 3/3 işleniyor...
2025-06-08 01:25:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 280 -> 229
2025-06-08 01:25:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:25:26 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:25:26 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['- dabılyu dabılyu dabılyu nokta example nokta kom sitesi - test nokta te er adresi  Bu bir sır.', '.', 'Fısıltı testi yapıyoruz.', 'Korkunç hikaye anlatılıyor   Mutlu son geldi!', 'Gülme efekti Test bitti Başarılı Sistem mükemmel çalışıyor.']
2025-06-08 01:25:33 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 6.689
2025-06-08 01:25:33 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.294
2025-06-08 01:25:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:25:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 1255486 sample
2025-06-08 01:25:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/ultimate_comprehensive.wav
2025-06-08 01:25:33 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:41:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:41:11 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:41:11 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:41:12 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:41:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:41:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:41:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:41:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 17
2025-06-08 01:41:21 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:21 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:21 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran 1.beş artmış']
2025-06-08 01:41:22 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.333
2025-06-08 01:41:22 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.589
2025-06-08 01:41:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_fix_v2_1.wav
2025-06-08 01:41:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 21 -> 22
2025-06-08 01:41:22 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:22 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:22 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Değer 2.beş hesaplandı']
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.798
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.299
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_fix_v2_2.wav
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 17 -> 21
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sonuç üç çeyrek çıktı']
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.674
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.279
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_fix_v2_3.wav
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 32
2025-06-08 01:41:23 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:23 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Miktar dört üç çeyrek belirlendi']
2025-06-08 01:41:24 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.891
2025-06-08 01:41:24 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:41:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_fix_v2_4.wav
2025-06-08 01:41:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 14
2025-06-08 01:41:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:24 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:24 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['1 madde önemli']
2025-06-08 01:41:25 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.605
2025-06-08 01:41:25 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.286
2025-06-08 01:41:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_fix_v2_5.wav
2025-06-08 01:41:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 19 -> 17
2025-06-08 01:41:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:25 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:25 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['2 paragraf da var']
2025-06-08 01:41:25 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.546
2025-06-08 01:41:25 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:41:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:25 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/decimal_fix_v2_6.wav
2025-06-08 01:41:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:41:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:41:26 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:41:26 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:41:26 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:41:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:41:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:41:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:41:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 24
2025-06-08 01:41:35 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:35 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:35 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir test Devam ediyor']
2025-06-08 01:41:36 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.621
2025-06-08 01:41:36 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.286
2025-06-08 01:41:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_pause_1.wav
2025-06-08 01:41:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 18 -> 16
2025-06-08 01:41:36 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:36 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:36 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba nasılsın']
2025-06-08 01:41:37 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.693
2025-06-08 01:41:37 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.362
2025-06-08 01:41:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_pause_2.wav
2025-06-08 01:41:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 21
2025-06-08 01:41:37 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:37 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:37 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu çok güzel.', '.', 'Harika']
2025-06-08 01:41:38 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.285
2025-06-08 01:41:38 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.269
2025-06-08 01:41:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_pause_3.wav
2025-06-08 01:41:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 31 -> 28
2025-06-08 01:41:38 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:38 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:38 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Birinci ikinci üçüncü sırada']
2025-06-08 01:41:39 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.774
2025-06-08 01:41:39 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:41:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/punctuation_pause_4.wav
2025-06-08 01:41:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:41:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:41:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:41:39 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:41:39 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:41:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:41:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:41:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:41:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 36 -> 30
2025-06-08 01:41:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma    kısa bekleme']
2025-06-08 01:41:49 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.803
2025-06-08 01:41:49 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.306
2025-06-08 01:41:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/pause_code_1.wav
2025-06-08 01:41:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 29 -> 27
2025-06-08 01:41:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:49 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:49 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Başlangıç      orta bekleme']
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.789
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.319
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/pause_code_2.wav
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 24
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Test        uzun bekleme']
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.534
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.272
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/pause_code_3.wav
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 33 -> 40
2025-06-08 01:41:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:41:50 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Çok          çok uzun            bekleme']
2025-06-08 01:41:51 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.564
2025-06-08 01:41:51 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.267
2025-06-08 01:41:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:41:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/pause_code_4.wav
2025-06-08 01:41:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:41:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:41:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:41:51 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:41:51 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:42:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:42:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:42:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:42:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 21 -> 29
2025-06-08 01:42:01 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:42:01 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:42:01 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['test nokta te re sitesi güzel']
2025-06-08 01:42:02 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.843
2025-06-08 01:42:02 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.304
2025-06-08 01:42:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:42:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/web_address_1.wav
2025-06-08 01:42:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 54
2025-06-08 01:42:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:42:02 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:42:02 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['dabılyu dabılyu dabılyu nokta example nokta kom adresi']
2025-06-08 01:42:03 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.658
2025-06-08 01:42:03 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.346
2025-06-08 01:42:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:42:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/web_address_2.wav
2025-06-08 01:42:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 31 -> 42
2025-06-08 01:42:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:42:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:42:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['site nokta org ve test nokta net adresleri']
2025-06-08 01:42:05 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.321
2025-06-08 01:42:05 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.304
2025-06-08 01:42:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:42:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/web_address_3.wav
2025-06-08 01:42:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:42:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:42:05 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:42:05 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:42:05 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (65 kelime), chunk'lara bölünüyor...
2025-06-08 01:42:14 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 2 parçaya bölündü (toplam 65 kelime)
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 2 chunk'a bölündü
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/2 işleniyor...
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 239 -> 228
2025-06-08 01:42:14 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:42:14 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:42:14 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba!', 'Bu son düzeltmeler testidir      Ondalık sayılar: - Oran 1.beş artmış - Değer 2.beş hesaplandı - Sonuç üç çeyrek çıktı - Miktar dört üç çeyrek belirlendi        Sıralama sayıları: 1 Bu birinci madde 2 Bu ikinci paragraf']
2025-06-08 01:42:19 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 5.064
2025-06-08 01:42:19 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.310
2025-06-08 01:42:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:42:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/2 işleniyor...
2025-06-08 01:42:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 312 -> 293
2025-06-08 01:42:20 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:42:20 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:42:20 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sıralama sayıları: 1 Bu birinci madde 2 Bu ikinci paragraf          Noktalama testleri: Bu bir test Devam ediyor güzel.', '.', 'Harika Web adresleri: - test nokta te re sitesi - dabılyu dabılyu dabılyu nokta example nokta kom adresi    Bu bir sır.', '.', 'Fısıltı testi.', 'Mutlu son!', 'Test bitti Başarılı.']
2025-06-08 01:42:29 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 9.563
2025-06-08 01:42:29 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.306
2025-06-08 01:42:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:42:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 1056103 sample
2025-06-08 01:42:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/comprehensive_v2.wav
2025-06-08 01:42:29 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:51:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:51:39 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:51:39 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:51:39 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:51:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:51:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:51:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:51:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 16 -> 21
2025-06-08 01:51:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:48 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:48 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran bir buçuk artmış']
2025-06-08 01:51:49 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.363
2025-06-08 01:51:49 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.531
2025-06-08 01:51:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_1.wav
2025-06-08 01:51:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 21 -> 26
2025-06-08 01:51:49 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:49 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:49 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Değer iki buçuk hesaplandı']
2025-06-08 01:51:50 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.733
2025-06-08 01:51:50 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.296
2025-06-08 01:51:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_2.wav
2025-06-08 01:51:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 17 -> 21
2025-06-08 01:51:50 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:50 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:50 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Sonuç üç çeyrek çıktı']
2025-06-08 01:51:51 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.702
2025-06-08 01:51:51 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.284
2025-06-08 01:51:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_3.wav
2025-06-08 01:51:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 32
2025-06-08 01:51:51 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:51 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:51 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Miktar dört üç çeyrek belirlendi']
2025-06-08 01:51:52 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.946
2025-06-08 01:51:52 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.308
2025-06-08 01:51:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_4.wav
2025-06-08 01:51:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 26 -> 24
2025-06-08 01:51:52 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:52 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:52 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir test Devam ediyor']
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.633
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.285
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_5.wav
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 36 -> 34
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma        uzun bekleme']
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.724
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.282
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_6.wav
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 21 -> 29
2025-06-08 01:51:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:53 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['test nokta te re sitesi güzel']
2025-06-08 01:51:54 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.756
2025-06-08 01:51:54 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.278
2025-06-08 01:51:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_7.wav
2025-06-08 01:51:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 41 -> 21
2025-06-08 01:51:54 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:54 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:54 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Heyecan!', 'Normal devam']
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.787
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.270
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_8.wav
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 11 -> 10
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu bir sır']
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.363
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.267
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_9.wav
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 23 -> 54
2025-06-08 01:51:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:51:55 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['dabılyu dabılyu dabılyu nokta example nokta kom adresi']
2025-06-08 01:51:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.732
2025-06-08 01:51:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.286
2025-06-08 01:51:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:51:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/final_perfect_10.wav
2025-06-08 01:51:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 01:51:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 01:51:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 01:51:57 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 01:51:58 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (70 kelime), chunk'lara bölünüyor...
2025-06-08 01:52:07 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 3 parçaya bölündü (toplam 70 kelime)
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 3 chunk'a bölündü
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/3 işleniyor...
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 275 -> 272
2025-06-08 01:52:07 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:52:07 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:52:07 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba arkadaşlar!', 'RecLastTTS sisteminin mükemmel halini test ediyoruz      Ondalık sayılar: - Oran bir buçuk artmış - Değer iki buçuk hesaplandı - Sonuç üç çeyrek çıktı - Miktar dört üç çeyrek belirlendi        Noktalama testleri: Bu bir test Devam ediyor güzel.', '.', 'Harika']
2025-06-08 01:52:08 | src.reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: enable_text_splitting=True requires Spacy: pip install spacy[ja]
2025-06-08 01:52:08 | src.reclasttts.core.tts_engine | [33mWARNING[0m | Chunk 1 işlenemedi, atlanıyor...
2025-06-08 01:52:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/3 işleniyor...
2025-06-08 01:52:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 341 -> 311
2025-06-08 01:52:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:52:08 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:52:08 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Noktalama testleri: Bu bir test Devam ediyor güzel.', '.', 'Harika          Web adresleri: - test nokta te re sitesi - dabılyu dabılyu dabılyu nokta example nokta kom adresi    Bu bir sır.', '.', 'Fısıltı testi yapıyoruz.', 'Korkunç hikaye anlatılıyor         Mutlu son geldi!', 'Gülme efekti Test bitti Başarılı']
2025-06-08 01:52:15 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 7.751
2025-06-08 01:52:15 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.280
2025-06-08 01:52:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:52:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 3/3 işleniyor...
2025-06-08 01:52:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 123 -> 84
2025-06-08 01:52:16 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 01:52:16 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 01:52:16 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Mutlu son geldi!', 'Gülme efekti Test bitti Başarılı Sistem mükemmel çalışıyor.']
2025-06-08 01:52:18 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.171
2025-06-08 01:52:18 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.279
2025-06-08 01:52:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 01:52:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin işleme tamamlandı: 788583 sample
2025-06-08 01:52:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/ultimate_final.wav
2025-06-08 01:52:18 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 03:34:48 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 41 -> 191 karakter
2025-06-08 03:34:48 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 36 -> 361 karakter
2025-06-08 03:34:48 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 32 -> 593 karakter
2025-06-08 03:34:48 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 36 -> 311 karakter
2025-06-08 03:34:48 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 31 -> 316 karakter
2025-06-08 03:34:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 03:34:48 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 03:34:48 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 03:34:49 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 03:34:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 03:34:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 03:34:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 03:34:59 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 39 -> 191 karakter
2025-06-08 03:34:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 43 -> 191
2025-06-08 03:34:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:34:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:34:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba Nasılsın Ben iyiyim teşekkürler']
2025-06-08 03:35:02 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.688
2025-06-08 03:35:02 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.641
2025-06-08 03:35:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:35:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/advanced_prosody_1.wav
2025-06-08 03:35:02 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 47 -> 237 karakter
2025-06-08 03:35:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 49 -> 237
2025-06-08 03:35:02 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:02 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:02 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu çok önemli bir konu Dikkatli dinleyin lütfen']
2025-06-08 03:35:03 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.476
2025-06-08 03:35:03 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.406
2025-06-08 03:35:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:35:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/advanced_prosody_2.wav
2025-06-08 03:35:03 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 45 -> 372 karakter
2025-06-08 03:35:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 48 -> 372
2025-06-08 03:35:03 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:03 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:03 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['<prosody rate="1.2" pitch="+0.3st" volume="+0.2dB">Gerçekten mi İnanamıyorum Bu harika bir haber']
2025-06-08 03:35:08 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 4.121
2025-06-08 03:35:08 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.483
2025-06-08 03:35:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:35:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/advanced_prosody_3.wav
2025-06-08 03:35:08 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 46 -> 158 karakter
2025-06-08 03:35:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 48 -> 158
2025-06-08 03:35:08 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:08 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:08 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Üzgünüm Bu durumda yapabileceğim bir şey yok']
2025-06-08 03:35:10 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.914
2025-06-08 03:35:10 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.559
2025-06-08 03:35:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:35:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/advanced_prosody_4.wav
2025-06-08 03:35:10 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 46 -> 236 karakter
2025-06-08 03:35:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 49 -> 236
2025-06-08 03:35:10 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:10 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:10 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Dikkat Tehlikeli bir durum var Hemen uzaklaşın']
2025-06-08 03:35:12 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 2.112
2025-06-08 03:35:12 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.551
2025-06-08 03:35:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:35:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/advanced_prosody_5.wav
2025-06-08 03:35:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 03:35:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 03:35:12 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 03:35:12 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 03:35:12 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Uzun metin tespit edildi (76 kelime), chunk'lara bölünüyor...
2025-06-08 03:35:24 | src.reclasttts.utils.text_processor | [32mINFO[0m | Uzun metin 4 parçaya bölündü (toplam 76 kelime)
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Metin 4 chunk'a bölündü
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 1/4 işleniyor...
2025-06-08 03:35:24 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 167 -> 1163 karakter
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 173 -> 1163
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba arkadaşlar Bugün çok özel bir gün Size önemli bir haber vermek istiyorum Hazır mısınız RecLastTTS <prosody rate="1.1" pitch="-0.1st" volume="-0.1dB">sistemimiz artık gelişmiş <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">prosodi desteğine sahip Bu ne demek']
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: enable_text_splitting=True requires Spacy: pip install spacy[ja]
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [33mWARNING[0m | Chunk 1 işlenemedi, atlanıyor...
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 2/4 işleniyor...
2025-06-08 03:35:24 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 197 -> 1581 karakter
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 203 -> 1581
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['RecLastTTS <prosody rate="1.1" pitch="-0.1st" volume="-0.1dB">sistemimiz artık gelişmiş <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">prosodi desteğine sahip Bu ne demek <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">Şimdi açıklayayım: Birincisi noktalama işaretleri doğal duraklamalar <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">yapıyor İkincisi soru cümleleri yükselen tonla okunuyor']
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: enable_text_splitting=True requires Spacy: pip install spacy[ja]
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [33mWARNING[0m | Chunk 2 işlenemedi, atlanıyor...
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 3/4 işleniyor...
2025-06-08 03:35:24 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 217 -> 2027 karakter
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 225 -> 2027
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['işaretleri doğal duraklamalar <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">yapıyor İkincisi soru cümleleri yükselen tonla okunuyor Üçüncüsü ünlem cümleleri vurgulu şekilde ifade <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">ediliyor Peki ya fiiller Onlar da özel <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">prosodi kurallarıyla <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">işleniyor <prosody rate="1.1" pitch="-0.1st" volume="-0.1dB"><prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">Gelmiyor musun']
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: enable_text_splitting=True requires Spacy: pip install spacy[ja]
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [33mWARNING[0m | Chunk 3 işlenemedi, atlanıyor...
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Chunk 4/4 işleniyor...
2025-06-08 03:35:24 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 184 -> 1764 karakter
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 193 -> 1764
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:35:24 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['ya fiiller Onlar da özel <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">prosodi kurallarıyla <prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">işleniyor <prosody rate="1.1" pitch="-0.1st" volume="-0.1dB"><prosody rate="1.0" pitch="+0.0st" volume="+0.0dB">Gelmiyor musun <prosody rate="1.1" pitch="-0.1st" volume="-0.1dB">Gelmez mi acaba Bu tür sorular artık doğal Harika <prosody rate="1.2" pitch="+0.3st" volume="+0.2dB">değil mi Ben çok memnunum Siz de öyle olacaksınız Teşekkürler']
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [31mERROR[0m | TTS hatası: enable_text_splitting=True requires Spacy: pip install spacy[ja]
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [33mWARNING[0m | Chunk 4 işlenemedi, atlanıyor...
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [31mERROR[0m | Hiçbir chunk işlenemedi
2025-06-08 03:35:24 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi
2025-06-08 03:38:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | RecLastTTS Engine başlatılıyor - Device: cuda
2025-06-08 03:38:43 | src.reclasttts.core.tts_engine | [32mINFO[0m | XTTS v2 modeli yükleniyor...
2025-06-08 03:38:43 | TTS.utils.manage | [32mINFO[0m | tts_models/multilingual/multi-dataset/xtts_v2 is already downloaded.
2025-06-08 03:38:44 | TTS.tts.models | [32mINFO[0m | Using model: xtts
2025-06-08 03:38:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Model GPU'ya yüklendi
2025-06-08 03:38:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | ✅ XTTS v2 modeli başarıyla yüklendi
2025-06-08 03:38:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker hazır: temp\default_speaker\default_speaker.wav
2025-06-08 03:38:53 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 27 -> 27 karakter
2025-06-08 03:38:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 30 -> 27
2025-06-08 03:38:53 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:38:53 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:38:53 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Merhaba Nasılsın Ben iyiyim']
2025-06-08 03:38:55 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.668
2025-06-08 03:38:55 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.674
2025-06-08 03:38:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:38:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/simple_prosody_1.wav
2025-06-08 03:38:55 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 38 -> 38 karakter
2025-06-08 03:38:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 41 -> 38
2025-06-08 03:38:55 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:38:55 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:38:55 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Bu çok önemli Dikkatli dinleyin lütfen']
2025-06-08 03:38:56 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.983
2025-06-08 03:38:56 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.309
2025-06-08 03:38:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:38:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/simple_prosody_2.wav
2025-06-08 03:38:56 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 34 -> 34 karakter
2025-06-08 03:38:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 37 -> 34
2025-06-08 03:38:56 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:38:56 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:38:56 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Gerçekten mi İnanamıyorum Harika.', '.']
2025-06-08 03:38:57 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.340
2025-06-08 03:38:57 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.296
2025-06-08 03:38:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:38:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/simple_prosody_3.wav
2025-06-08 03:38:57 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 37 -> 37 karakter
2025-06-08 03:38:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 33 -> 37
2025-06-08 03:38:57 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:38:57 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:38:57 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Oran bir buçuk artmış Test tamamlandı']
2025-06-08 03:38:59 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 1.030
2025-06-08 03:38:59 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.301
2025-06-08 03:38:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:38:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/simple_prosody_4.wav
2025-06-08 03:38:59 | src.reclasttts.core.turkish_prosody_engine | [32mINFO[0m | Türkçe prosodi uygulandı: 42 -> 42 karakter
2025-06-08 03:38:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başlıyor - Dil: tr, Uzunluk: 43 -> 41
2025-06-08 03:38:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Varsayılan speaker kullanılıyor
2025-06-08 03:38:59 | TTS.utils.synthesizer | [32mINFO[0m | Text split into sentences.
2025-06-08 03:38:59 | TTS.utils.synthesizer | [32mINFO[0m | Input: ['Normal konuşma      bekleme sonrası devam']
2025-06-08 03:38:59 | TTS.utils.synthesizer | [32mINFO[0m | Processing time: 0.944
2025-06-08 03:38:59 | TTS.utils.synthesizer | [32mINFO[0m | Real-time factor: 0.307
2025-06-08 03:38:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | TTS işlemi başarılı
2025-06-08 03:38:59 | src.reclasttts.core.tts_engine | [32mINFO[0m | Ses dosyası kaydedildi: output/simple_prosody_5.wav
2025-06-08 03:39:00 | src.reclasttts.core.tts_engine | [32mINFO[0m | Kaynaklar temizlendi

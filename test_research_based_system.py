#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Research Based System Test
Araştırma temelli sistem testi
"""

import os
import time
from src.reclasttts.core.tts_engine import RecLastTTSEngine
from src.reclasttts.core.turkish_prosody_engine import get_turkish_prosody_engine
from src.reclasttts.core.xtts_prosody_controller import get_xtts_prosody_controller


def test_turkish_prosody_research():
    """Türkçe prosodi araştırması testi"""
    print("\n🔬 Türkçe Prosodi Araştırması Testi")
    print("=" * 50)
    print("📚 Baran Uslu'nun araştırmasından ilham alınarak geliştirildi")
    
    prosody_engine = get_turkish_prosody_engine()
    
    # Türkçe fiil çekim testleri
    verb_tests = [
        {
            "text": "Geliyorum. Gelmiyorum. Geliyor musun? Gelmiyor musun?",
            "description": "Fiil çekim prosodisi",
            "expected": "Olumlu/olumsuz fiiller ve soru tonlaması"
        },
        {
            "text": "Bu kitabı okuyorum. Bu kitabı okumuyorum.",
            "description": "Olumsuz fiil prosodisi",
            "expected": "Olumsuz fiillerde farklı vurgu"
        },
        {
            "text": "Nereye gidiyorsun? Nereye gitmiyorsun?",
            "description": "Soru fiil prosodisi",
            "expected": "Soru cümlelerinde yükselen ezgi"
        },
        {
            "text": "Çok güzel, harika, mükemmel bir gün!",
            "description": "Virgül ve ünlem prosodisi",
            "expected": "Virgül duraklamaları ve ünlem vurgusu"
        },
        {
            "text": "Belki gelir... Belki de gelmez.",
            "description": "Üç nokta prosodisi",
            "expected": "Uzun duraklama ve düşen ton"
        }
    ]
    
    for i, test in enumerate(verb_tests, 1):
        print(f"\n📝 Test {i} - {test['description']}:")
        print(f"Orijinal: {test['text']}")
        print(f"Beklenen: {test['expected']}")
        
        # Prosodi işleme
        processed = prosody_engine.process_turkish_prosody(test['text'])
        print(f"İşlenmiş: {processed}")
        
        # Prosodi özellikleri
        features = prosody_engine.extract_prosody_features(test['text'])
        print(f"Özellikler: {features}")


def test_xtts_parametric_control():
    """XTTS parametrik kontrol testi"""
    print("\n🎛️ XTTS Parametrik Kontrol Testi")
    print("=" * 50)
    
    prosody_controller = get_xtts_prosody_controller()
    
    # Base parametreler
    base_params = {
        'temperature': 0.65,
        'repetition_penalty': 8.0,
        'top_p': 0.75,
        'top_k': 45
    }
    
    # Duygu prosodi modifikasyonları
    emotion_tests = [
        {
            'emotion': 'happy',
            'prosody': {'pitch_mod': 0.2, 'duration_mod': 1.1, 'energy_mod': 0.15}
        },
        {
            'emotion': 'sad',
            'prosody': {'pitch_mod': -0.15, 'duration_mod': 1.2, 'energy_mod': -0.2}
        },
        {
            'emotion': 'angry',
            'prosody': {'pitch_mod': 0.3, 'duration_mod': 0.9, 'energy_mod': 0.3}
        },
        {
            'emotion': 'whisper',
            'prosody': {'pitch_mod': -0.1, 'duration_mod': 1.4, 'energy_mod': -0.4}
        }
    ]
    
    for test in emotion_tests:
        print(f"\n📝 {test['emotion'].upper()} Duygu Testi:")
        print(f"Prosodi Params: {test['prosody']}")
        
        # Parametreleri uygula
        modified_params = prosody_controller.apply_prosody_to_generation(
            "Test metni", test['prosody'], base_params.copy()
        )
        
        print(f"Base Params: {base_params}")
        print(f"Modified Params: {modified_params}")
        
        # Değişiklikleri göster
        for key in base_params:
            if key in modified_params:
                change = modified_params[key] - base_params[key]
                print(f"  {key}: {base_params[key]} -> {modified_params[key]} (Δ{change:+.2f})")


def test_research_based_tts():
    """Araştırma temelli TTS testi"""
    print("\n🚀 Araştırma Temelli TTS Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Araştırma temelli testler
    research_tests = [
        {
            "text": "Merhaba! Nasılsın? Ben iyiyim, teşekkürler.",
            "emotion": "happy",
            "description": "Mutlu selamlama - fiil prosodisi"
        },
        {
            "text": "Bu çok önemli bir konu. Dikkatli dinleyin, lütfen.",
            "emotion": "neutral",
            "description": "Ciddi açıklama - noktalama prosodisi"
        },
        {
            "text": "Gelmiyor musun? Gelmez mi acaba? Bu çok üzücü...",
            "emotion": "sad",
            "description": "Üzgün soru - fiil çekim prosodisi"
        },
        {
            "text": "Harika! Mükemmel! Bu gerçekten çok güzel bir haber.",
            "emotion": "excited",
            "description": "Heyecan - ünlem prosodisi"
        },
        {
            "text": "Dikkat... Çok tehlikeli bir durum var. Uzaklaşın!",
            "emotion": "scary",
            "description": "Uyarı - üç nokta ve ünlem prosodisi"
        }
    ]
    
    for i, test in enumerate(research_tests, 1):
        print(f"\n📝 Test {i} - {test['description']}:")
        print(f"Metin: {test['text']}")
        print(f"Duygu: {test['emotion']}")
        
        # TTS ile test
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion=test['emotion']
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/research_based_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ TTS Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
                print(f"🎯 Kontrol: {test['description']}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_comprehensive_research():
    """Kapsamlı araştırma testi"""
    print("\n🎬 Kapsamlı Araştırma Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Tüm araştırma bulgularını içeren test
    comprehensive_text = """
    Merhaba arkadaşlar! Bugün Türkçe TTS araştırması sonuçlarını test ediyoruz.
    
    Baran Uslu'nun araştırmasından öğrendiklerimiz:
    
    Birincisi, fiil çekimleri özel prosodi kuralları gerektirir.
    Geliyorum, gelmiyorum, geliyor musun, gelmiyor musun?
    
    İkincisi, noktalama işaretleri doğal duraklamalar yapar.
    Bu çok önemli. Dikkatli dinleyin, lütfen... Anladınız mı?
    
    Üçüncüsü, duygu kontrolü parametrik olarak yapılır.
    Mutlu tonlama! Üzgün ifade... Kızgın ses tonu!
    
    Sonuç olarak, araştırma temelli yaklaşım çok daha doğal.
    
    Teşekkürler!
    """
    
    print("📝 Kapsamlı araştırma test metni hazırlandı")
    print(f"📊 Metin uzunluğu: {len(comprehensive_text)} karakter")
    
    start_time = time.time()
    audio_data = engine.text_to_speech(
        text=comprehensive_text,
        language="tr",
        emotion="neutral",
        enable_chunking=True,
        chunk_size=50
    )
    end_time = time.time()
    
    if audio_data is not None:
        output_path = "output/comprehensive_research.wav"
        os.makedirs("output", exist_ok=True)
        success = engine.save_audio(audio_data, output_path)
        
        if success:
            duration = len(audio_data) / 22050
            word_count = len(comprehensive_text.split())
            print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
            print(f"📁 Kaydedildi: {output_path}")
            print(f"📊 {word_count} kelime, {word_count / (end_time - start_time):.1f} kelime/saniye")
            print(f"🎯 Araştırma temelli kaliteyi kontrol edin!")
        else:
            print("❌ Ses kaydedilemedi")
    else:
        print("❌ TTS işlemi başarısız")


def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS Research Based System Test")
    print("=" * 60)
    print("🎯 Araştırma temelli sistem test ediliyor...")
    print("\n📚 Bilimsel Temeller:")
    print("• Baran Uslu - Türkçe TTS prosodi araştırması")
    print("• XTTS v2 parametrik kontrol optimizasyonu")
    print("• Türkçe fiil çekim prosodi kuralları")
    print("• Noktalama tabanlı ezgi kontrolü")
    print("• Duygu bazında parametrik modifikasyon")
    
    try:
        # Testleri çalıştır
        test_turkish_prosody_research()
        test_xtts_parametric_control()
        test_research_based_tts()
        test_comprehensive_research()
        
        print("\n🎉 Araştırma temelli sistem testleri tamamlandı!")
        print("📁 Ses dosyaları 'output/' klasöründe kaydedildi.")
        print("\n📋 Araştırma Temelli Özellikler:")
        print("✅ Türkçe fiil çekim prosodi kuralları")
        print("✅ Noktalama tabanlı ezgi kontrolü")
        print("✅ XTTS v2 parametrik optimizasyon")
        print("✅ Duygu bazında prosodi modifikasyonu")
        print("✅ Bilimsel araştırma temelli yaklaşım")
        print("\n🎧 Ses dosyalarını dinleyerek araştırma temelli kaliteyi kontrol edin!")
        print("\n⚠️ Özellikle kontrol edilecekler:")
        print("1. Fiil çekimlerinde doğal vurgu var mı?")
        print("2. Noktalarda düşen ton var mı?")
        print("3. Soru cümlelerinde yükselen ezgi var mı?")
        print("4. Virgüllerde kısa duraklamalar var mı?")
        print("5. Duygu değişimleri doğal mı?")
        print("6. Genel konuşma akıcılığı nasıl?")
        print("\n🚀 Bu araştırma temelli yaklaşım daha doğal mı?")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

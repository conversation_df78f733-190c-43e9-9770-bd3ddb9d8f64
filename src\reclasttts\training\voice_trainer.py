#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Voice Training System for YouTube Content
YouTube ses verilerinizle özel model eğitimi
"""

import os
import json
import librosa
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from ..utils.logger import get_logger


class YouTubeVoiceTrainer:
    """YouTube ses verilerinizle özel ses modeli eğitimi"""
    
    def __init__(self, training_dir: str = "training_data"):
        self.logger = get_logger(__name__)
        self.training_dir = Path(training_dir)
        self.training_dir.mkdir(exist_ok=True)
        
        # Eğitim konfigürasyonu
        self.config = {
            "sample_rate": 22050,
            "min_audio_length": 1.0,  # minimum 1 saniye
            "max_audio_length": 10.0,  # maksimum 10 saniye
            "target_db": -20,  # hedef ses seviyesi
            "emotion_categories": [
                "neutral", "happy", "sad", "angry", "excited", 
                "calm", "whisper", "shout", "scary"
            ]
        }
        
        # Ses ka<PERSON><PERSON> kriterle<PERSON>
        self.quality_thresholds = {
            "min_snr": 15,  # Signal-to-Noise Ratio
            "max_silence_ratio": 0.3,  # Maksimum sessizlik oranı
            "min_speech_clarity": 0.7  # Konuşma netliği
        }
    
    def extract_audio_from_youtube(self, youtube_url: str, output_path: str) -> bool:
        """
        YouTube videosundan ses çıkar
        Not: yt-dlp kütüphanesi gerekli
        """
        try:
            import yt_dlp
            
            ydl_opts = {
                'format': 'bestaudio/best',
                'outtmpl': output_path,
                'postprocessors': [{
                    'key': 'FFmpegExtractAudio',
                    'preferredcodec': 'wav',
                    'preferredquality': '192',
                }],
            }
            
            with yt_dlp.YoutubeDL(ydl_opts) as ydl:
                ydl.download([youtube_url])
            
            self.logger.info(f"YouTube ses çıkarıldı: {output_path}")
            return True
            
        except ImportError:
            self.logger.error("yt-dlp kütüphanesi bulunamadı. 'pip install yt-dlp' ile yükleyin")
            return False
        except Exception as e:
            self.logger.error(f"YouTube ses çıkarma hatası: {e}")
            return False
    
    def prepare_training_data(self, audio_files: List[str], transcripts: List[str] = None) -> Dict:
        """
        Eğitim verilerini hazırla
        
        Args:
            audio_files: Ses dosyası yolları
            transcripts: Metin transkriptleri (opsiyonel)
        """
        training_data = {
            "audio_segments": [],
            "metadata": {
                "total_duration": 0,
                "quality_stats": {},
                "emotion_distribution": {}
            }
        }
        
        for i, audio_file in enumerate(audio_files):
            try:
                # Ses dosyasını yükle
                audio, sr = librosa.load(audio_file, sr=self.config["sample_rate"])
                
                # Ses kalitesini analiz et
                quality_score = self._analyze_audio_quality(audio, sr)
                
                if quality_score["overall"] < 0.6:
                    self.logger.warning(f"Düşük kalite ses atlandı: {audio_file}")
                    continue
                
                # Ses segmentlerini çıkar
                segments = self._extract_speech_segments(audio, sr)
                
                for j, segment in enumerate(segments):
                    segment_data = {
                        "audio_path": audio_file,
                        "segment_id": f"{i}_{j}",
                        "start_time": segment["start"],
                        "end_time": segment["end"],
                        "duration": segment["duration"],
                        "quality_score": quality_score,
                        "transcript": transcripts[i] if transcripts and i < len(transcripts) else "",
                        "emotion": self._detect_emotion(segment["audio"])
                    }
                    
                    training_data["audio_segments"].append(segment_data)
                    training_data["metadata"]["total_duration"] += segment["duration"]
                
                self.logger.info(f"İşlendi: {audio_file} - {len(segments)} segment")
                
            except Exception as e:
                self.logger.error(f"Ses işleme hatası {audio_file}: {e}")
                continue
        
        # Metadata'yı güncelle
        self._update_training_metadata(training_data)
        
        # Eğitim verisini kaydet
        output_file = self.training_dir / "training_data.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(training_data, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"Eğitim verisi hazırlandı: {len(training_data['audio_segments'])} segment")
        return training_data
    
    def _analyze_audio_quality(self, audio: np.ndarray, sr: int) -> Dict:
        """Ses kalitesini analiz et"""
        try:
            # Signal-to-Noise Ratio
            signal_power = np.mean(audio ** 2)
            noise_power = np.mean((audio - np.mean(audio)) ** 2)
            snr = 10 * np.log10(signal_power / (noise_power + 1e-10))
            
            # Sessizlik oranı
            silence_threshold = 0.01
            silence_ratio = np.sum(np.abs(audio) < silence_threshold) / len(audio)
            
            # Spektral netlik
            stft = librosa.stft(audio)
            spectral_clarity = np.mean(np.abs(stft))
            
            # Genel kalite skoru
            snr_score = min(1.0, max(0.0, (snr - 10) / 20))
            silence_score = max(0.0, 1.0 - silence_ratio / 0.5)
            clarity_score = min(1.0, spectral_clarity / 0.1)
            
            overall_score = (snr_score + silence_score + clarity_score) / 3
            
            return {
                "snr": snr,
                "silence_ratio": silence_ratio,
                "spectral_clarity": spectral_clarity,
                "snr_score": snr_score,
                "silence_score": silence_score,
                "clarity_score": clarity_score,
                "overall": overall_score
            }
            
        except Exception as e:
            self.logger.error(f"Kalite analizi hatası: {e}")
            return {"overall": 0.0}
    
    def _extract_speech_segments(self, audio: np.ndarray, sr: int) -> List[Dict]:
        """Konuşma segmentlerini çıkar"""
        try:
            # Ses aktivitesi tespiti
            frame_length = int(0.025 * sr)  # 25ms
            hop_length = int(0.010 * sr)    # 10ms
            
            # RMS enerji hesapla
            rms = librosa.feature.rms(y=audio, frame_length=frame_length, hop_length=hop_length)[0]
            
            # Eşik değeri
            threshold = np.percentile(rms, 30)
            
            # Konuşma bölgelerini bul
            speech_frames = rms > threshold
            
            # Frame'leri zamana çevir
            times = librosa.frames_to_time(np.arange(len(speech_frames)), sr=sr, hop_length=hop_length)
            
            # Sürekli konuşma bölgelerini grupla
            segments = []
            start_time = None
            
            for i, is_speech in enumerate(speech_frames):
                if is_speech and start_time is None:
                    start_time = times[i]
                elif not is_speech and start_time is not None:
                    end_time = times[i]
                    duration = end_time - start_time
                    
                    # Minimum ve maksimum süre kontrolü
                    if (self.config["min_audio_length"] <= duration <= self.config["max_audio_length"]):
                        start_sample = int(start_time * sr)
                        end_sample = int(end_time * sr)
                        segment_audio = audio[start_sample:end_sample]
                        
                        segments.append({
                            "start": start_time,
                            "end": end_time,
                            "duration": duration,
                            "audio": segment_audio
                        })
                    
                    start_time = None
            
            return segments
            
        except Exception as e:
            self.logger.error(f"Segment çıkarma hatası: {e}")
            return []
    
    def _detect_emotion(self, audio: np.ndarray) -> str:
        """
        Basit duygu tespiti (gelecekte ML modeli ile geliştirilebilir)
        """
        try:
            # Basit özellik çıkarımı
            energy = np.mean(audio ** 2)
            zero_crossing_rate = np.mean(librosa.feature.zero_crossing_rate(audio))
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(y=audio))
            
            # Basit kural tabanlı sınıflandırma
            if energy > 0.1 and spectral_centroid > 2000:
                return "excited"
            elif energy < 0.01:
                return "whisper"
            elif energy > 0.05 and zero_crossing_rate > 0.1:
                return "angry"
            elif spectral_centroid < 1000:
                return "sad"
            elif energy > 0.03:
                return "happy"
            else:
                return "neutral"
                
        except Exception as e:
            self.logger.error(f"Duygu tespit hatası: {e}")
            return "neutral"
    
    def _update_training_metadata(self, training_data: Dict):
        """Eğitim metadata'sını güncelle"""
        segments = training_data["audio_segments"]
        
        # Duygu dağılımı
        emotion_counts = {}
        for segment in segments:
            emotion = segment["emotion"]
            emotion_counts[emotion] = emotion_counts.get(emotion, 0) + 1
        
        training_data["metadata"]["emotion_distribution"] = emotion_counts
        
        # Kalite istatistikleri
        quality_scores = [s["quality_score"]["overall"] for s in segments]
        training_data["metadata"]["quality_stats"] = {
            "mean_quality": np.mean(quality_scores),
            "min_quality": np.min(quality_scores),
            "max_quality": np.max(quality_scores),
            "total_segments": len(segments)
        }
    
    def create_voice_clone_dataset(self, training_data: Dict, voice_name: str) -> str:
        """
        Ses klonu için dataset oluştur
        """
        try:
            voice_dir = self.training_dir / "voice_clones" / voice_name
            voice_dir.mkdir(parents=True, exist_ok=True)
            
            # En kaliteli segmentleri seç
            segments = training_data["audio_segments"]
            high_quality_segments = [
                s for s in segments 
                if s["quality_score"]["overall"] > 0.8
            ]
            
            # Duygu bazında dengeli seçim
            selected_segments = self._balance_emotion_selection(high_quality_segments)
            
            # Ses dosyalarını kopyala ve organize et
            dataset_info = {
                "voice_name": voice_name,
                "total_segments": len(selected_segments),
                "total_duration": sum(s["duration"] for s in selected_segments),
                "emotion_distribution": {},
                "files": []
            }
            
            for i, segment in enumerate(selected_segments):
                # Ses dosyasını yükle ve kaydet
                audio, sr = librosa.load(segment["audio_path"], sr=self.config["sample_rate"])
                start_sample = int(segment["start_time"] * sr)
                end_sample = int(segment["end_time"] * sr)
                segment_audio = audio[start_sample:end_sample]
                
                # Dosya adı
                filename = f"{voice_name}_{segment['emotion']}_{i:04d}.wav"
                filepath = voice_dir / filename
                
                # Ses dosyasını kaydet
                librosa.output.write_wav(str(filepath), segment_audio, sr)
                
                dataset_info["files"].append({
                    "filename": filename,
                    "emotion": segment["emotion"],
                    "duration": segment["duration"],
                    "quality": segment["quality_score"]["overall"]
                })
            
            # Dataset bilgilerini kaydet
            info_file = voice_dir / "dataset_info.json"
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(dataset_info, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"Ses klonu dataset'i oluşturuldu: {voice_dir}")
            return str(voice_dir)
            
        except Exception as e:
            self.logger.error(f"Dataset oluşturma hatası: {e}")
            return ""
    
    def _balance_emotion_selection(self, segments: List[Dict], max_per_emotion: int = 10) -> List[Dict]:
        """Duygu bazında dengeli segment seçimi"""
        emotion_groups = {}
        
        # Duygu bazında grupla
        for segment in segments:
            emotion = segment["emotion"]
            if emotion not in emotion_groups:
                emotion_groups[emotion] = []
            emotion_groups[emotion].append(segment)
        
        # Her duygudan en fazla max_per_emotion kadar seç
        selected = []
        for emotion, group in emotion_groups.items():
            # Kalite bazında sırala
            group.sort(key=lambda x: x["quality_score"]["overall"], reverse=True)
            selected.extend(group[:max_per_emotion])
        
        return selected


# Global instance
_voice_trainer = None

def get_voice_trainer() -> YouTubeVoiceTrainer:
    """Global YouTubeVoiceTrainer instance'ını al"""
    global _voice_trainer
    if _voice_trainer is None:
        _voice_trainer = YouTubeVoiceTrainer()
    return _voice_trainer

{"model": "xtts", "run_name": "xtts_v2_turkish_finetune", "run_description": "XTTS v2 Turkish fine-tuning with custom voice", "datasets": [{"name": "turkish_custom", "path": "fine_tuning\\dataset", "meta_file_train": "metadata.csv", "meta_file_val": "metadata.csv", "language": "tr", "formatter": "l<PERSON><PERSON><PERSON><PERSON>"}], "model_args": {"gpt_batch_size": 2, "enable_redaction": false, "kv_cache": true, "gpt_max_audio_len": 229376, "gpt_max_text_len": 400, "gpt_max_prompt_len": 70, "gpt_layers": 30, "gpt_n_model_channels": 1024, "gpt_n_heads": 16, "gpt_number_text_tokens": 255, "gpt_start_text_token": 255, "gpt_checkpointing": true, "gpt_loss_text_ce_weight": 0.01, "gpt_loss_mel_ce_weight": 1.0, "gpt_use_masking_gt_prompt_approach": true, "gpt_use_mup": true}, "batch_size": 2, "eval_batch_size": 2, "num_loader_workers": 0, "num_eval_loader_workers": 0, "run_eval": true, "test_delay_epochs": -1, "epochs": 100, "lr": 1e-05, "optimizer": "AdamW", "optimizer_params": {"betas": [0.9, 0.96], "eps": 1e-08, "weight_decay": 0.01}, "lr_scheduler": "MultiStepLR", "lr_scheduler_params": {"milestones": [50, 150, 300], "gamma": 0.5}, "audio": {"sample_rate": 22050, "hop_length": 256, "win_length": 1024, "n_mel_channels": 80, "mel_fmin": 0, "mel_fmax": 8000}, "output_path": "fine_tuning\\output", "save_step": 10, "save_n_checkpoints": 5, "save_checkpoints": true, "print_step": 1, "plot_step": 10, "log_model_step": 100, "use_cuda": true, "mixed_precision": true, "precision": "fp16", "restore_path": "tts_models/multilingual/multi-dataset/xtts_v2", "continue_path": null, "strict_loading": false}
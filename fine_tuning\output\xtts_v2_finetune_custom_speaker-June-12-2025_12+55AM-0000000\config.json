{"output_path": "fine_tuning\\output", "logger_uri": null, "run_name": "xtts_v2_finetune_custom_speaker", "project_name": null, "run_description": "XTTS v2 fine-tuning for custom_speaker", "print_step": 25, "plot_step": 100, "model_param_stats": false, "wandb_entity": null, "dashboard_logger": "tensorboard", "save_on_interrupt": true, "log_model_step": 1000, "save_step": 1000, "save_n_checkpoints": 1, "save_checkpoints": true, "save_all_best": false, "save_best_after": 0, "target_loss": null, "print_eval": false, "test_delay_epochs": -1, "run_eval": true, "run_eval_steps": null, "distributed_backend": "nccl", "distributed_url": "tcp://localhost:54321", "mixed_precision": false, "precision": "fp16", "epochs": 100, "batch_size": 2, "eval_batch_size": 2, "grad_clip": 0.0, "scheduler_after_epoch": true, "lr": 1e-05, "optimizer": "AdamW", "optimizer_params": {"betas": [0.9, 0.96], "eps": 1e-08, "weight_decay": 0.01}, "lr_scheduler": "MultiStepLR", "lr_scheduler_params": {"milestones": [50000, 150000, 300000], "gamma": 0.5, "last_epoch": -1}, "use_grad_scaler": false, "allow_tf32": false, "cudnn_enable": true, "cudnn_deterministic": false, "cudnn_benchmark": false, "training_seed": 54321, "model": "xtts", "num_loader_workers": 0, "num_eval_loader_workers": 0, "use_noise_augment": false, "audio": {"sample_rate": 22050, "output_sample_rate": 24000, "dvae_sample_rate": 22050}, "use_phonemes": false, "phonemizer": null, "phoneme_language": null, "compute_input_seq_cache": false, "text_cleaner": null, "enable_eos_bos_chars": false, "test_sentences_file": "", "phoneme_cache_path": null, "characters": null, "add_blank": false, "batch_group_size": 0, "loss_masking": null, "min_audio_len": 1, "max_audio_len": Infinity, "min_text_len": 1, "max_text_len": Infinity, "compute_f0": false, "compute_energy": false, "compute_linear_spec": false, "precompute_num_workers": 0, "start_by_longest": false, "shuffle": false, "drop_last": false, "datasets": [{"formatter": "l<PERSON><PERSON><PERSON><PERSON>", "dataset_name": "", "path": "fine_tuning\\dataset", "meta_file_train": "metadata.csv", "ignored_speakers": null, "language": "tr", "phonemizer": "", "meta_file_val": "metadata.csv", "meta_file_attn_mask": ""}], "test_sentences": [], "eval_split_max_size": null, "eval_split_size": 0.01, "use_speaker_weighted_sampler": false, "speaker_weighted_sampler_alpha": 1.0, "use_language_weighted_sampler": false, "language_weighted_sampler_alpha": 1.0, "use_length_weighted_sampler": false, "length_weighted_sampler_alpha": 1.0, "model_args": {"gpt_batch_size": 2, "enable_redaction": false, "kv_cache": true, "gpt_checkpoint": null, "clvp_checkpoint": null, "decoder_checkpoint": null, "num_chars": 255, "tokenizer_file": "", "gpt_max_audio_tokens": 605, "gpt_max_text_tokens": 402, "gpt_max_prompt_tokens": 70, "gpt_layers": 30, "gpt_n_model_channels": 1024, "gpt_n_heads": 16, "gpt_number_text_tokens": 255, "gpt_start_text_token": 255, "gpt_stop_text_token": null, "gpt_num_audio_tokens": 8194, "gpt_start_audio_token": 8192, "gpt_stop_audio_token": 8193, "gpt_code_stride_len": 1024, "gpt_use_masking_gt_prompt_approach": true, "gpt_use_perceiver_resampler": false, "input_sample_rate": 22050, "output_sample_rate": 24000, "output_hop_length": 256, "decoder_input_dim": 1024, "d_vector_dim": 512, "cond_d_vector_in_each_upsampling_layer": true, "duration_const": 102400}, "model_dir": null, "languages": ["en", "es", "fr", "de", "it", "pt", "pl", "tr", "ru", "nl", "cs", "ar", "zh-cn", "hu", "ko", "ja", "hi"], "temperature": 0.85, "length_penalty": 1.0, "repetition_penalty": 2.0, "top_k": 50, "top_p": 0.85, "num_gpt_outputs": 1, "gpt_cond_len": 12, "gpt_cond_chunk_len": 4, "max_ref_len": 10, "sound_norm_refs": false}
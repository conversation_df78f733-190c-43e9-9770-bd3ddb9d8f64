#!/usr/bin/env python3
"""
RecLastTTS İyileştirmeler Test Scripti
Yeni özellikleri test eder:
- Uzun metin desteği (50.000 kelime)
- <PERSON><PERSON><PERSON> kodları
- Bekleme kodları
- Türkçe optimizasyonları
"""

import os
import sys
import time

# Proje kök dizinini sys.path'e ekle
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

from src.reclasttts.core.tts_engine import RecLastTTSEngine
from src.reclasttts.utils.text_processor import get_text_processor
from src.reclasttts.core.config import get_config


def test_emotion_codes():
    """Duygu kodlarını test et"""
    print("\n🎭 Duygu Kodları Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    test_texts = [
        "Merhaba! <laugh> Bu çok komik bir durum. <excited>Gerçekten heyecan verici!</excited>",
        "<scary><PERSON>u korkunç bir hikaye...</scary> <#1#> Ama sonunda <happy>mutlu son var!</happy>",
        "<whisper>Bu bir sır...</whisper> <#0.5#> <shout>Ama şimdi herkese söylüyorum!</shout>",
        "Normal konuşma. <#2#> Uzun bir bekleme sonrası devam ediyoruz."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Test {i}: {text[:50]}...")
        
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=text,
            language="tr",
            emotion="neutral"
        )
        end_time = time.time()
        
        if audio_data is not None:
            # Ses dosyasını kaydet
            output_path = f"output/emotion_test_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050  # 22kHz sample rate
                print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_long_text():
    """Uzun metin işlemeyi test et"""
    print("\n📚 Uzun Metin Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Uzun test metni oluştur (yaklaşık 500 kelime)
    base_text = """
    Türkiye, Anadolu yarımadası ve Trakya'nın küçük bir bölümünde yer alan bir ülkedir. 
    Başkenti Ankara, en büyük şehri ise İstanbul'dur. <#0.5#> 
    
    <excited>Türkiye'nin tarihi çok zengindir!</excited> Anadolu toprakları, tarih boyunca 
    birçok medeniyete ev sahipliği yapmıştır. <#1#> Hitit, Frig, Lidya, Pers, Makedon, 
    Roma, Bizans ve Osmanlı gibi büyük imparatorluklar bu topraklarda hüküm sürmüştür.
    
    <calm>Modern Türkiye Cumhuriyeti, Mustafa Kemal Atatürk önderliğinde 1923 yılında kurulmuştur.</calm>
    <#0.5#> Atatürk, ülkeyi çağdaş medeniyetler seviyesine çıkarmak için birçok devrim gerçekleştirmiştir.
    
    <happy>Türkiye, coğrafi konumu itibariyle Avrupa ile Asya arasında köprü görevi görmektedir.</happy>
    Bu stratejik konum, ülkeyi tarih boyunca önemli kılmıştır. <#1#>
    
    Türk mutfağı, dünya mutfakları arasında özel bir yere sahiptir. <laugh> Kebap, baklava, 
    Turkish delight gibi lezzetler dünya çapında tanınmaktadır.
    """
    
    # Metni 3 kez tekrarla (yaklaşık 1500 kelime)
    long_text = (base_text + " ") * 3
    
    word_count = len(long_text.split())
    print(f"📊 Test metni: {word_count} kelime, {len(long_text)} karakter")
    
    start_time = time.time()
    audio_data = engine.text_to_speech(
        text=long_text,
        language="tr",
        emotion="neutral",
        enable_chunking=True,
        chunk_size=100
    )
    end_time = time.time()
    
    if audio_data is not None:
        # Ses dosyasını kaydet
        output_path = "output/long_text_test.wav"
        os.makedirs("output", exist_ok=True)
        success = engine.save_audio(audio_data, output_path)
        
        if success:
            duration = len(audio_data) / 22050
            processing_time = end_time - start_time
            print(f"✅ Başarılı: {processing_time:.2f}s işlem, {duration:.2f}s ses")
            print(f"📁 Kaydedildi: {output_path}")
            print(f"⚡ Hız: {word_count / processing_time:.1f} kelime/saniye")
        else:
            print("❌ Ses kaydedilemedi")
    else:
        print("❌ Uzun metin işlemi başarısız")


def test_turkish_optimization():
    """Türkçe optimizasyonlarını test et"""
    print("\n🇹🇷 Türkçe Optimizasyon Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    test_texts = [
        "Merhaba, nasılsınız? Bugün hava çok güzel, değil mi?",
        "Türkçe karakterler: ğüşıöç. Bu karakterlerin telaffuzu önemlidir.",
        "Dr. Ahmet, Prof. Mehmet ile vs. konuları konuştu. Örn. matematik, fizik vb.",
        "Sayılar: 1. madde, 2. paragraf, 3. bölüm şeklinde sıralanır.",
        "İnternet adresleri: www.example.com, test.tr gibi örnekler vardır.",
        "Karışık dil: Resident Evil serisinin yeni oyunu Requiem, Şubat 2026'da çıkacak.",
        "Web siteleri: www.umiteski.com.tr sitemizi ziyaret edin. test.tr de güzel.",
        "Noktalama: Bu bir test... Gerçekten mi? Evet! Harika, değil mi?",
        "Kısaltmalar düzeltmesi: Örn. bu metin Dr. ve Prof. içeriyor."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Test {i}: {text}")
        
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=text,
            language="tr",
            emotion="neutral"
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/turkish_test_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_text_processor():
    """Text processor'ın yeni özelliklerini test et"""
    print("\n🔧 Text Processor Testi")
    print("=" * 50)
    
    processor = get_text_processor()
    
    test_texts = [
        "Normal metin <#0.5#> bekleme ile <laugh> gülme efekti.",
        "<excited>Heyecanlı metin!</excited> <#1#> <scary>Korkunç kısım...</scary>",
        "Çok uzun bir metin " * 50,  # 150 kelime
        "Sayılar: 1, 2, 3, 10, 100, 1000 şeklinde yazılır."
    ]
    
    for i, text in enumerate(test_texts, 1):
        print(f"\n📝 Test {i}: {text[:50]}...")
        
        # Metin işleme
        processed = processor.process_text(text, "tr", enable_ssml=True)
        print(f"🔄 İşlenmiş: {processed[:100]}...")
        
        # Dinamik bekleme işleme
        with_pauses = processor.process_dynamic_pause(processed)
        print(f"⏱️ Beklemeli: {with_pauses[:100]}...")
        
        # Uzun metin bölme
        if len(text.split()) > 50:
            chunks = processor.split_long_text(text, max_words=30)
            print(f"📚 {len(chunks)} parçaya bölündü")
            for j, chunk in enumerate(chunks[:3], 1):  # İlk 3 parçayı göster
                print(f"   Parça {j}: {chunk[:50]}...")
        
        # Süre tahmini
        estimated_duration = processor.estimate_speech_duration(text)
        print(f"⏰ Tahmini süre: {estimated_duration:.1f} saniye")


def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS İyileştirmeler Test Scripti")
    print("=" * 60)
    
    try:
        # Konfigürasyonu kontrol et
        config = get_config()
        print(f"📋 Konfigürasyon yüklendi: {config.model.name}")
        print(f"🎯 Maksimum metin uzunluğu: {config.model.max_text_length}")
        print(f"🔧 Chunk boyutu: {getattr(config.model, 'chunk_size', 100)}")
        
        # Testleri çalıştır
        test_text_processor()
        test_emotion_codes()
        test_turkish_optimization()
        test_long_text()
        
        print("\n🎉 Tüm testler tamamlandı!")
        print("📁 Ses dosyaları 'output/' klasöründe kaydedildi.")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

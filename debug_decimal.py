#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug Decimal Processing
Ondalık işleme debug
"""

import re

# Test pattern
decimal_pattern = re.compile(r'\b(\d+)\.(\d+)\b')

# Test text
test_text = "Oran 1.5 artmış. Değer 2.5 hesaplandı. Sonuç 3.25 çıktı."

# Find matches
matches = decimal_pattern.findall(test_text)

print("Decimal matches:")
for whole, decimal in matches:
    print(f"  whole='{whole}' (type: {type(whole)})")
    print(f"  decimal='{decimal}' (type: {type(decimal)})")
    print()

# Test conversions
decimal_conversions = {
    '5': ' buçuk',      # .5 -> buçuk
    '25': ' çeyrek',    # .25 -> çeyrek  
    '75': ' üç çeyrek', # .75 -> üç çeyrek
}

turkish_numbers = {
    '1': 'bir', '2': 'iki', '3': 'üç', '4': 'dört'
}

print("Conversion test:")
for whole, decimal in matches:
    print(f"Processing: {whole}.{decimal}")
    print(f"  decimal='{decimal}' in conversions: {decimal in decimal_conversions}")
    
    if decimal in decimal_conversions:
        whole_word = turkish_numbers.get(whole, whole)
        replacement = f"{whole_word}{decimal_conversions[decimal]}"
        print(f"  Result: {replacement}")
    else:
        print(f"  No conversion found for '{decimal}'")
    print()

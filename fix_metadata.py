#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Metadata Fixer
Mevcut metadata dosyasını düzelt
"""

import os
from pathlib import Path


def fix_metadata():
    """Metadata dosyasını düzelt"""
    print("🔧 Metadata dosyası düzeltiliyor...")
    
    dataset_path = Path("fine_tuning/dataset")
    metadata_path = dataset_path / "metadata.csv"
    
    if not metadata_path.exists():
        print("❌ Metadata dosyası bulunamadı")
        return False
    
    # Mevcut metadata'yı oku
    with open(metadata_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 Mevcut satır sayısı: {len(lines)}")
    
    # Yeni format: audio_file|text (sadece 2 alan)
    new_lines = []
    for line in lines:
        parts = line.strip().split('|')
        if len(parts) >= 2:
            # İlk iki alanı al: audio_file|text
            new_line = f"{parts[0]}|{parts[1]}"
            new_lines.append(new_line)
    
    # <PERSON><PERSON>zeltilmiş metadata'yı kaydet
    with open(metadata_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(new_lines))
    
    print(f"✅ Metadata düzeltildi: {len(new_lines)} satır")
    print(f"📝 Yeni format örneği: {new_lines[0] if new_lines else 'Boş'}")
    
    return True


def main():
    """Ana fonksiyon"""
    print("🔧 Metadata Fixer")
    print("=" * 30)
    
    if fix_metadata():
        print("\n🎉 Metadata başarıyla düzeltildi!")
        print("\n🚀 Sonraki adımlar:")
        print("1. Web UI'de Fine-tuning tab'ına git")
        print("2. Eğitimi yeniden başlat")
        print("3. Düzeltilmiş format kullanılacak")
    else:
        print("\n❌ Metadata düzeltme başarısız")


if __name__ == "__main__":
    main()

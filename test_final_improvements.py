#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RecLastTTS Final Improvements Test
Gelişmiş test scripti - tüm iyileştirmeleri test eder
"""

import os
import time
from src.reclasttts.core.tts_engine import RecLastTTSEngine
from src.reclasttts.core.config import get_config
from src.reclasttts.utils.text_processor import get_text_processor


def test_emotion_improvements():
    """Gelişmiş duygu testleri"""
    print("\n🎭 Gelişmiş Duygu Testleri")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Test metinleri - her duygu için optimize edilmiş
    emotion_tests = [
        {
            "emotion": "happy",
            "text": "Bugün harika bir gün! Çok mutluyum ve keyifliyim. Her şey mükemmel gidiyor!",
            "expected": "Mutlu ve enerjik ses tonu"
        },
        {
            "emotion": "sad", 
            "text": "Çok üzgünüm... Bu gerçekten zor bir durum. Kalbimdeki acı tarif edilemez.",
            "expected": "Yavaş ve melankolik ses tonu"
        },
        {
            "emotion": "angry",
            "text": "Bu kabul edilemez! Artık sabrım taştı. Bu duruma bir son verilmeli!",
            "expected": "Sert ve hızlı ses tonu"
        },
        {
            "emotion": "scary",
            "text": "Karanlık koridorda ayak sesleri duyuluyordu... Birisi yaklaşıyordu...",
            "expected": "Alçak ve gizemli ses tonu"
        },
        {
            "emotion": "excited",
            "text": "İnanılmaz! Bu gerçekten muhteşem! Hayatımın en güzel günü!",
            "expected": "Hızlı ve coşkulu ses tonu"
        },
        {
            "emotion": "whisper",
            "text": "Bu bir sır... Kimseye söyleme. Sadece sen biliyorsun.",
            "expected": "Fısıltı ses tonu"
        }
    ]
    
    for i, test in enumerate(emotion_tests, 1):
        print(f"\n📝 Test {i} - {test['emotion'].upper()}:")
        print(f"Metin: {test['text']}")
        print(f"Beklenen: {test['expected']}")
        
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion=test['emotion']
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/emotion_improved_{test['emotion']}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_punctuation_improvements():
    """Noktalama iyileştirmeleri testi"""
    print("\n📝 Noktalama İyileştirmeleri Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    punctuation_tests = [
        "Bu bir test... Gerçekten mi? Evet! Harika, değil mi?",
        "Birinci madde, ikinci paragraf, üçüncü bölüm şeklinde sıralanır.",
        "Dr. Ahmet, Prof. Mehmet ile vs. konuları konuştu. Örn. matematik.",
        "Web siteleri: www.example.com, test.tr gibi örnekler vardır.",
        "Türkçe karakterler: ğüşıöç telaffuzu önemlidir.",
        "Karışık dil: Resident Evil oyunu February 2026'da çıkacak."
    ]
    
    for i, text in enumerate(punctuation_tests, 1):
        print(f"\n📝 Test {i}: {text}")
        
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=text,
            language="tr",
            emotion="neutral"
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/punctuation_test_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_special_codes():
    """Özel kodlar testi"""
    print("\n🔧 Özel Kodlar Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    special_tests = [
        "Normal konuşma <#1#> bir saniye bekleme <#2#> iki saniye bekleme.",
        "Gülme efekti: <laugh> Bu çok komik! Normal devam.",
        "<excited>Heyecanlı başlangıç!</excited> <#0.5#> <calm>Sakin devam.</calm>",
        "<whisper>Bu bir sır</whisper> <#1#> <shout>Ama şimdi bağırıyorum!</shout>",
        "Karışık: <scary>Korkunç</scary> <#0.5#> <happy>ama mutlu son!</happy>"
    ]
    
    for i, text in enumerate(special_tests, 1):
        print(f"\n📝 Test {i}: {text}")
        
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=text,
            language="tr",
            emotion="neutral"
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/special_codes_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_comprehensive_scenario():
    """Kapsamlı senaryo testi"""
    print("\n🎬 Kapsamlı Senaryo Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Gerçek kullanım senaryosu
    scenario_text = """
    <excited>Merhaba arkadaşlar!</excited> Bugün sizlerle RecLastTTS sistemini test ediyoruz.
    
    <#1#>
    
    Bu sistem şu özelliklere sahip:
    1. Türkçe ve İngilizce dil desteği
    2. Duygu kodları: <happy>mutlu</happy>, <sad>üzgün</sad>, <angry>kızgın</angry>
    3. Bekleme kodları: <#0.5#> kısa, <#2#> uzun beklemeler
    
    <#1#>
    
    <whisper>Gizli bilgi:</whisper> Sistem www.example.com adresinde çalışıyor.
    Dr. Ahmet ve Prof. Mehmet bu projeyi geliştirdi.
    
    <#0.5#>
    
    <scary>Dikkat!</scary> Test sırasında beklenmedik durumlar olabilir...
    <#1#>
    <laugh> Şaka şaka! <happy>Her şey yolunda!</happy>
    
    <calm>Test tamamlandı. Teşekkürler.</calm>
    """
    
    print("📝 Kapsamlı senaryo metni hazırlandı")
    print(f"📊 Metin uzunluğu: {len(scenario_text)} karakter")
    
    start_time = time.time()
    audio_data = engine.text_to_speech(
        text=scenario_text,
        language="tr",
        emotion="neutral",
        enable_chunking=True,
        chunk_size=50
    )
    end_time = time.time()
    
    if audio_data is not None:
        output_path = "output/comprehensive_scenario.wav"
        os.makedirs("output", exist_ok=True)
        success = engine.save_audio(audio_data, output_path)
        
        if success:
            duration = len(audio_data) / 22050
            word_count = len(scenario_text.split())
            print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
            print(f"📁 Kaydedildi: {output_path}")
            print(f"📊 {word_count} kelime, {word_count / (end_time - start_time):.1f} kelime/saniye")
        else:
            print("❌ Ses kaydedilemedi")
    else:
        print("❌ TTS işlemi başarısız")


def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS Final Improvements Test")
    print("=" * 60)
    
    try:
        # Konfigürasyonu kontrol et
        config = get_config()
        print(f"📋 Konfigürasyon: {config.model.name}")
        print(f"🎯 Device: {config.get_device()}")
        
        # Testleri çalıştır
        test_emotion_improvements()
        test_punctuation_improvements() 
        test_special_codes()
        test_comprehensive_scenario()
        
        print("\n🎉 Tüm testler tamamlandı!")
        print("📁 Ses dosyaları 'output/' klasöründe kaydedildi.")
        print("\n📋 Test Sonuçları:")
        print("✅ Duygu parametreleri optimize edildi")
        print("✅ Noktalama işleme iyileştirildi")
        print("✅ Özel kodlar düzgün çalışıyor")
        print("✅ TTS uyarıları düzeltildi")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

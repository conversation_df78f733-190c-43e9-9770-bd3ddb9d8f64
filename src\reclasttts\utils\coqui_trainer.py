#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Coqui TTS Real Fine-tuning
Gerçek Coqui TTS fine-tuning implementasyonu
"""

import os
import sys
import json
import torch
import shutil
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Callable
from ..utils.logger import get_logger


class CoquiFineTuner:
    """Gerçek Coqui TTS fine-tuning"""
    
    def __init__(self, dataset_path: str, output_path: str):
        self.dataset_path = Path(dataset_path)
        self.output_path = Path(output_path)
        self.logger = get_logger(__name__)
        self.progress_callback = None
        
        # Device kontrolü
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.logger.info(f"🎮 Device: {self.device}")
        
        # Output klasörünü oluştur
        self.output_path.mkdir(parents=True, exist_ok=True)
    
    def set_progress_callback(self, callback: Callable[[str, int], None]):
        """Progress callback ayarla"""
        self.progress_callback = callback
    
    def _update_progress(self, message: str, progress: int):
        """Progress güncelle"""
        self.logger.info(message)
        if self.progress_callback:
            self.progress_callback(message, progress)
    
    def prepare_xtts_config(self, epochs: int = 100, batch_size: int = 2, lr: float = 1e-5) -> str:
        """XTTS fine-tuning config dosyası oluştur"""
        try:
            self._update_progress("XTTS config hazırlanıyor...", 10)
            
            # XTTS v2 için gerçek config
            config = {
                "model": "xtts",
                "run_name": "xtts_v2_turkish_finetune",
                "run_description": "XTTS v2 Turkish fine-tuning with custom voice",
                
                # Dataset config
                "datasets": [
                    {
                        "name": "turkish_custom",
                        "path": str(self.dataset_path),
                        "meta_file_train": "metadata.csv",
                        "meta_file_val": "metadata.csv",
                        "language": "tr",
                        "formatter": "ljspeech"
                    }
                ],
                
                # Model args
                "model_args": {
                    "gpt_batch_size": batch_size,
                    "enable_redaction": False,
                    "kv_cache": True,
                    "gpt_max_audio_len": 229376,
                    "gpt_max_text_len": 400,
                    "gpt_max_prompt_len": 70,
                    "gpt_layers": 30,
                    "gpt_n_model_channels": 1024,
                    "gpt_n_heads": 16,
                    "gpt_number_text_tokens": 255,
                    "gpt_start_text_token": 255,
                    "gpt_checkpointing": True,
                    "gpt_loss_text_ce_weight": 0.01,
                    "gpt_loss_mel_ce_weight": 1.0,
                    "gpt_use_masking_gt_prompt_approach": True,
                    "gpt_use_mup": True
                },
                
                # Training config
                "batch_size": batch_size,
                "eval_batch_size": batch_size,
                "num_loader_workers": 0,
                "num_eval_loader_workers": 0,
                "run_eval": True,
                "test_delay_epochs": -1,
                
                # Optimizer
                "epochs": epochs,
                "lr": lr,
                "optimizer": "AdamW",
                "optimizer_params": {
                    "betas": [0.9, 0.96],
                    "eps": 1e-8,
                    "weight_decay": 1e-2
                },
                
                # Scheduler
                "lr_scheduler": "MultiStepLR",
                "lr_scheduler_params": {
                    "milestones": [50, 150, 300],
                    "gamma": 0.5
                },
                
                # Audio config
                "audio": {
                    "sample_rate": 22050,
                    "hop_length": 256,
                    "win_length": 1024,
                    "n_mel_channels": 80,
                    "mel_fmin": 0,
                    "mel_fmax": 8000
                },
                
                # Output config
                "output_path": str(self.output_path),
                "save_step": 10,
                "save_n_checkpoints": 5,
                "save_checkpoints": True,
                "print_step": 1,
                "plot_step": 10,
                "log_model_step": 100,
                
                # GPU config
                "use_cuda": torch.cuda.is_available(),
                "mixed_precision": True,
                "precision": "fp16" if torch.cuda.is_available() else "fp32",
                
                # Fine-tuning specific
                "restore_path": "tts_models/multilingual/multi-dataset/xtts_v2",
                "continue_path": None,
                "strict_loading": False
            }
            
            # Config dosyasını kaydet
            config_path = self.output_path / "xtts_config.json"
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            self._update_progress("✅ XTTS config hazırlandı", 15)
            return str(config_path)
            
        except Exception as e:
            self.logger.error(f"❌ Config hazırlama hatası: {e}")
            return ""
    
    def run_coqui_training(self, config_path: str) -> bool:
        """Coqui TTS ile gerçek training"""
        try:
            self._update_progress("🚀 Coqui TTS training başlatılıyor...", 20)
            
            # Training command hazırla
            cmd = [
                sys.executable, "-m", "TTS.bin.train_tts",
                "--config_path", config_path,
                "--restore_path", "tts_models/multilingual/multi-dataset/xtts_v2",
                "--output_path", str(self.output_path),
                "--use_cuda", str(torch.cuda.is_available()).lower()
            ]
            
            self.logger.info(f"🎯 Training command: {' '.join(cmd)}")
            
            # Training'i başlat
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=str(self.output_path),
                env=os.environ.copy()
            )
            
            # Progress takibi
            epoch_count = 0
            total_epochs = 100
            
            for line in iter(process.stdout.readline, ''):
                line = line.strip()
                if not line:
                    continue
                
                self.logger.info(f"Training: {line}")
                
                # Epoch progress
                if "epoch" in line.lower() and "/" in line:
                    try:
                        # Epoch sayısını çıkar
                        parts = line.split()
                        for part in parts:
                            if "/" in part and part.replace("/", "").replace(".", "").isdigit():
                                current, total = part.split("/")
                                epoch_count = int(current)
                                total_epochs = int(total)
                                progress = 20 + int((epoch_count / total_epochs) * 70)
                                self._update_progress(f"Epoch {epoch_count}/{total_epochs}", progress)
                                break
                    except:
                        pass
                
                # Loss bilgisi
                elif "loss" in line.lower():
                    self._update_progress(f"Training: {line}", None)
                
                # Checkpoint bilgisi
                elif "checkpoint" in line.lower():
                    self._update_progress(f"Checkpoint: {line}", None)
            
            # Process'in bitmesini bekle
            return_code = process.wait()
            
            if return_code == 0:
                self._update_progress("✅ Coqui training tamamlandı!", 95)
                return True
            else:
                self._update_progress("❌ Coqui training başarısız", 0)
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Coqui training hatası: {e}")
            self._update_progress(f"❌ Training hatası: {e}", 0)
            return False
    
    def create_inference_model(self) -> bool:
        """Inference için model hazırla"""
        try:
            self._update_progress("📦 Inference model hazırlanıyor...", 95)
            
            # En iyi checkpoint'i bul
            checkpoints = list(self.output_path.glob("**/best_model.pth"))
            if not checkpoints:
                checkpoints = list(self.output_path.glob("**/checkpoint_*.pth"))
            
            if checkpoints:
                best_checkpoint = max(checkpoints, key=lambda x: x.stat().st_mtime)
                
                # Model dosyasını kopyala
                final_model_path = self.output_path / "best_model.pth"
                if best_checkpoint != final_model_path:
                    shutil.copy2(best_checkpoint, final_model_path)
                
                # Config dosyasını kopyala
                config_files = list(self.output_path.glob("**/config.json"))
                if config_files:
                    final_config_path = self.output_path / "config.json"
                    shutil.copy2(config_files[0], final_config_path)
                
                self._update_progress("✅ Inference model hazır", 100)
                return True
            else:
                self.logger.error("❌ Checkpoint bulunamadı")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Inference model hatası: {e}")
            return False
    
    def run_full_pipeline(self, epochs: int = 100, batch_size: int = 2, lr: float = 1e-5) -> Dict:
        """Tam Coqui fine-tuning pipeline"""
        try:
            self.logger.info("🚀 Coqui TTS Fine-tuning Pipeline başlatılıyor...")
            
            # 1. Config hazırla
            config_path = self.prepare_xtts_config(epochs, batch_size, lr)
            if not config_path:
                return {"success": False, "error": "Config hazırlanamadı"}
            
            # 2. Training çalıştır
            if not self.run_coqui_training(config_path):
                return {"success": False, "error": "Training başarısız"}
            
            # 3. Inference model hazırla
            if not self.create_inference_model():
                return {"success": False, "error": "Inference model hazırlanamadı"}
            
            self.logger.info("🎉 Coqui Fine-tuning Pipeline tamamlandı!")
            
            return {
                "success": True,
                "model_path": str(self.output_path / "best_model.pth"),
                "config_path": str(self.output_path / "config.json"),
                "epochs": epochs,
                "device": self.device,
                "type": "coqui_real_training"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Pipeline hatası: {e}")
            return {"success": False, "error": str(e)}


def create_coqui_trainer(dataset_path: str, output_path: str) -> CoquiFineTuner:
    """Coqui trainer oluştur"""
    return CoquiFineTuner(dataset_path, output_path)

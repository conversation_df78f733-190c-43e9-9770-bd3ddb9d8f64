"""
RecLastTTS Metin İşleme Modülü
Doğal konuşma için metin ön-işleme ve SSML desteği
"""

import re
import unicodedata
from typing import Dict, List, Optional, Tuple
from ..utils.logger import get_logger


class TextProcessor:
    """Metin işleme ve SSML dönüştürme sınıfı"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Türkçe sayı çevirisi
        self.numbers_tr = {
            '0': 'sıfır', '1': 'bir', '2': 'iki', '3': 'üç', '4': 'dört',
            '5': 'beş', '6': 'altı', '7': 'yedi', '8': 'sekiz', '9': 'dokuz',
            '10': 'on', '11': 'on bir', '12': 'on iki', '13': 'on üç', '14': 'on dört',
            '15': 'on beş', '16': 'on altı', '17': 'on yedi', '18': 'on sekiz', '19': 'on dokuz',
            '20': 'yirmi', '30': 'otuz', '40': 'kırk', '50': 'elli',
            '60': 'altmış', '70': 'yetmiş', '80': 'seksen', '90': 'doksan',
            '100': 'yüz', '1000': 'bin'
        }
        
        # İngilizce sayı çevirisi
        self.numbers_en = {
            '0': 'zero', '1': 'one', '2': 'two', '3': 'three', '4': 'four',
            '5': 'five', '6': 'six', '7': 'seven', '8': 'eight', '9': 'nine',
            '10': 'ten', '11': 'eleven', '12': 'twelve', '13': 'thirteen', '14': 'fourteen',
            '15': 'fifteen', '16': 'sixteen', '17': 'seventeen', '18': 'eighteen', '19': 'nineteen',
            '20': 'twenty', '30': 'thirty', '40': 'forty', '50': 'fifty',
            '60': 'sixty', '70': 'seventy', '80': 'eighty', '90': 'ninety',
            '100': 'hundred', '1000': 'thousand'
        }
        
        # Noktalama işaretleri ve bekleme süreleri (Türkçe için optimize edilmiş)
        self.punctuation_breaks = {
            '.': '1200ms',   # Nokta - uzun bekleme (artırıldı)
            '!': '1200ms',   # Ünlem - uzun bekleme (artırıldı)
            '?': '1200ms',   # Soru - uzun bekleme (artırıldı)
            ',': '600ms',    # Virgül - orta bekleme (artırıldı)
            ';': '800ms',    # Noktalı virgül - orta bekleme (artırıldı)
            ':': '700ms',    # İki nokta - orta bekleme (artırıldı)
            '-': '400ms',    # Tire - kısa bekleme (artırıldı)
            '...': '1500ms', # Üç nokta - çok uzun bekleme (artırıldı)
        }
        
        # Gelişmiş özel komutlar sistemi
        self.special_commands = {
            # Eski bekleme komutları (geriye uyumluluk)
            '[PAUSE_SHORT]': '<break time="300ms"/>',
            '[PAUSE_MEDIUM]': '<break time="600ms"/>',
            '[PAUSE_LONG]': '<break time="1000ms"/>',
            '[PAUSE_EXTRA]': '<break time="1500ms"/>',
            '[BREATH]': '<break time="200ms"/>',
            '[SILENCE]': '<break time="2000ms"/>',

            # Yeni kısa kod sistemi - Bekleme (saniye bazında)
            '<#0.1#>': '<break time="100ms"/>',
            '<#0.2#>': '<break time="200ms"/>',
            '<#0.3#>': '<break time="300ms"/>',
            '<#0.5#>': '<break time="500ms"/>',
            '<#1#>': '<break time="1000ms"/>',
            '<#1.5#>': '<break time="1500ms"/>',
            '<#2#>': '<break time="2000ms"/>',
            '<#3#>': '<break time="3000ms"/>',
            '<#5#>': '<break time="5000ms"/>',

            # Duygu komutları (prosody ile)
            '<laugh>': '<prosody rate="fast" pitch="+10%">haha</prosody><break time="300ms"/>',
            '<scary>': '<prosody rate="slow" pitch="-20%">',
            '</scary>': '</prosody>',
            '<excited>': '<prosody rate="fast" pitch="+15%">',
            '</excited>': '</prosody>',
            '<sad>': '<prosody rate="slow" pitch="-10%">',
            '</sad>': '</prosody>',
            '<angry>': '<prosody rate="fast" pitch="+5%" volume="+10%">',
            '</angry>': '</prosody>',
            '<whisper>': '<prosody rate="slow" pitch="-5%" volume="-20%">',
            '</whisper>': '</prosody>',
            '<shout>': '<prosody rate="fast" pitch="+20%" volume="+20%">',
            '</shout>': '</prosody>',
            '<happy>': '<prosody rate="medium" pitch="+8%">',
            '</happy>': '</prosody>',
            '<calm>': '<prosody rate="slow" pitch="-5%">',
            '</calm>': '</prosody>',
        }

        # Uzun metin işleme için chunk boyutu (kelime bazında)
        self.max_chunk_words = 100  # ~400-500 karakter
        self.overlap_words = 10     # Chunk'lar arası örtüşme

        # Gelişmiş kısaltmalar ve özel durumlar
        self.turkish_abbreviations = {
            'Dr.': 'Doktor',
            'Prof.': 'Profesör',
            'Doç.': 'Doçent',
            'Yrd.': 'Yardımcı',
            'vs.': 've saire',
            'vb.': 've benzeri',
            'örn.': 'örneğin',
            'Örn.': 'Örneğin',
            'Inc.': 'Incorporated',
            'Ltd.': 'Limited',
            'Co.': 'Company',
            'Corp.': 'Corporation'
        }

        # Web adresi ve URL işleme
        self.web_patterns = {
            'www.': 'w w w nokta ',
            '.com': ' nokta com',
            '.tr': ' nokta tr',
            '.org': ' nokta org',
            '.net': ' nokta net',
            '.edu': ' nokta edu',
            '.gov': ' nokta gov',
            '.io': ' nokta i o'
        }

        # İngilizce kelimeler (basit tespit)
        self.english_words = {
            'example', 'test', 'hello', 'world', 'game', 'video', 'music',
            'resident', 'evil', 'requiem', 'call', 'duty', 'minecraft',
            'youtube', 'google', 'facebook', 'twitter', 'instagram',
            'windows', 'linux', 'android', 'iphone', 'samsung', 'apple'
        }
    
    def process_text(self, text: str, language: str = "tr", enable_ssml: bool = True) -> str:
        """
        Metni TTS için işle
        
        Args:
            text: İşlenecek metin
            language: Dil kodu (tr, en)
            enable_ssml: SSML desteği aktif mi
            
        Returns:
            İşlenmiş metin
        """
        try:
            if not text or not text.strip():
                return text
            
            # 1. Temel temizlik
            processed_text = self._clean_text(text)

            # 2. Özel komutları işle (bekleme ve duygu kodları)
            if enable_ssml:
                processed_text = self._process_special_commands(processed_text)
                processed_text = self.process_dynamic_pause(processed_text)

            # 3. Sayıları kelimeye çevir
            processed_text = self._convert_numbers(processed_text, language)

            # 4. Özel noktalama durumlarını işle
            processed_text = self._process_special_punctuation(processed_text)

            # 5. Noktalama işaretlerini doğal bekleme için işle
            processed_text = self._process_punctuation(processed_text)

            # 6. Dil özel durumları
            if language == "tr":
                processed_text = self._fix_turkish_specific(processed_text)
            elif language == "en":
                processed_text = self._fix_english_specific(processed_text)
            
            self.logger.debug(f"Metin işlendi: {len(text)} -> {len(processed_text)} karakter")
            return processed_text
            
        except Exception as e:
            self.logger.error(f"Metin işleme hatası: {e}")
            return text  # Hata durumunda orijinal metni döndür
    
    def _clean_text(self, text: str) -> str:
        """Metni temizle"""
        # Unicode normalizasyonu
        text = unicodedata.normalize('NFKC', text)
        
        # Çoklu boşlukları tek boşluğa çevir
        text = re.sub(r'\s+', ' ', text)
        
        # Başta ve sonda boşlukları kaldır
        text = text.strip()
        
        return text
    
    def _process_special_commands(self, text: str) -> str:
        """Özel bekleme komutlarını işle"""
        for command, ssml in self.special_commands.items():
            text = text.replace(command, ssml)
        return text
    
    def _convert_numbers(self, text: str, language: str) -> str:
        """Sayıları kelimeye çevir"""
        numbers_dict = self.numbers_tr if language == "tr" else self.numbers_en
        
        # Basit sayıları çevir (0-20, 30, 40, ..., 100, 1000)
        for num, word in numbers_dict.items():
            # Tam sayı eşleşmesi (kelime sınırları ile)
            pattern = r'\b' + re.escape(num) + r'\b'
            text = re.sub(pattern, word, text)
        
        return text
    
    def _process_punctuation(self, text: str) -> str:
        """Noktalama işaretlerini doğal bekleme için işle"""
        # SSML yerine noktalama işaretlerini koruyup çift boşluk ekle
        # XTTS v2 noktalama işaretlerini doğal olarak işler

        # Üç nokta özel işlem
        text = text.replace('...', '... ')

        # Noktalama işaretlerinden sonra ekstra boşluk ekle (doğal bekleme için)
        punctuation_spacing = {
            '.': '. ',    # Nokta sonrası boşluk
            '!': '! ',    # Ünlem sonrası boşluk
            '?': '? ',    # Soru sonrası boşluk
            ',': ', ',    # Virgül sonrası boşluk
            ';': '; ',    # Noktalı virgül sonrası boşluk
            ':': ': ',    # İki nokta sonrası boşluk
        }

        for punct, spaced_punct in punctuation_spacing.items():
            # Zaten boşluk varsa çift boşluk yapma
            text = re.sub(f'\\{punct}(?!\\s)', spaced_punct, text)

        return text
    
    def _fix_turkish_specific(self, text: str) -> str:
        """Türkçe özel durumları düzelt"""
        # 1. Kısaltmaları çevir
        for abbr, full in self.turkish_abbreviations.items():
            text = text.replace(abbr, full)

        # 2. Web adreslerini işle
        for pattern, replacement in self.web_patterns.items():
            text = text.replace(pattern, replacement)

        # 3. Sıralama sayılarını düzelt (1. -> birinci, 2. -> ikinci)
        ordinal_replacements = {
            '1.': 'birinci',
            '2.': 'ikinci',
            '3.': 'üçüncü',
            '4.': 'dördüncü',
            '5.': 'beşinci',
            '6.': 'altıncı',
            '7.': 'yedinci',
            '8.': 'sekizinci',
            '9.': 'dokuzuncu',
            '10.': 'onuncu'
        }

        # Sadece kelime sınırlarında değiştir (1.5 gibi ondalık sayıları korumak için)
        for ordinal, word in ordinal_replacements.items():
            pattern = r'\b' + re.escape(ordinal) + r'(?=\s|$)'
            text = re.sub(pattern, word, text)

        # 4. Türkçe karakter sorunları
        char_fixes = {
            'İ': 'i',  # XTTS büyük İ ile sorun yaşıyor
            'I': 'ı',  # İngilizce I'yı Türkçe ı'ya çevir
        }

        for old_char, new_char in char_fixes.items():
            text = text.replace(old_char, new_char)

        # 5. Çift noktalama temizle
        text = re.sub(r'\.{2,}', '.', text)
        text = re.sub(r'!{2,}', '!', text)
        text = re.sub(r'\?{2,}', '?', text)

        # 6. İngilizce kelimeleri tespit et ve işaretle
        text = self._handle_mixed_language(text)

        return text

    def _handle_mixed_language(self, text: str) -> str:
        """Karışık dil (TR/EN) metinleri işle"""
        words = text.split()
        processed_words = []

        for word in words:
            # Noktalama işaretlerini temizle
            clean_word = re.sub(r'[^\w]', '', word.lower())

            # İngilizce kelime tespiti
            if clean_word in self.english_words:
                # İngilizce kelimeyi işaretle (XTTS dil değiştirme için)
                processed_words.append(f'[EN]{word}[/EN]')
            else:
                processed_words.append(word)

        return ' '.join(processed_words)

    def _process_special_punctuation(self, text: str) -> str:
        """Özel noktalama durumlarını işle"""
        # SSML break etiketlerini temizle (çift işleme önlemek için)
        text = re.sub(r'<break[^>]*>', '', text)

        # Noktalama sonrası gereksiz sesler eklenmesini önle
        # "..." -> ". . ." yerine sadece "..." bırak
        text = re.sub(r'\.{3,}', '...', text)

        # Virgül sonrası çok kısa bekleme sorunu için
        # Virgülden sonra ekstra boşluk ekle
        text = re.sub(r',(?!\s)', ', ', text)

        # Noktalama işaretlerinin gereksiz seslendirilmesini önle
        # Çift noktalama temizle
        text = re.sub(r'\.{2,}', '.', text)
        text = re.sub(r'!{2,}', '!', text)
        text = re.sub(r'\?{2,}', '?', text)

        # Noktalama + boşluk + noktalama durumlarını temizle
        text = re.sub(r'([.!?])\s+([.!?])', r'\1\2', text)

        # Gereksiz noktalama kombinasyonlarını temizle
        text = re.sub(r'[.]{1,}\s*[.]{1,}', '.', text)

        return text

    def _fix_english_specific(self, text: str) -> str:
        """İngilizce özel durumları düzelt"""
        # İngilizce kısaltmalar
        english_abbreviations = {
            'Mr.': 'Mister',
            'Mrs.': 'Missis',
            'Ms.': 'Miss',
            'Dr.': 'Doctor',
            'Prof.': 'Professor',
            'Jr.': 'Junior',
            'Sr.': 'Senior',
            'vs.': 'versus',
            'etc.': 'etcetera'
        }

        for abbr, full in english_abbreviations.items():
            text = text.replace(abbr, full)

        # Web adresleri (İngilizce telaffuz)
        text = text.replace('www.', 'w w w dot ')
        text = text.replace('.com', ' dot com')
        text = text.replace('.org', ' dot org')

        return text

    def add_custom_pause(self, text: str, pause_duration: str) -> str:
        """
        Metne özel bekleme ekle
        
        Args:
            text: Metin
            pause_duration: Bekleme süresi (örn: "500ms", "1s")
            
        Returns:
            Bekleme eklenmiş metin
        """
        return f'{text}<break time="{pause_duration}"/>'
    
    def create_ssml_with_emotion(self, text: str, emotion: str = "neutral") -> str:
        """
        Duygu ile SSML oluştur

        Args:
            text: Metin
            emotion: Duygu (neutral, happy, sad, angry, excited, scary, whisper, shout)

        Returns:
            Duygulu SSML
        """
        emotion_settings = {
            "neutral": 'rate="medium" pitch="medium"',
            "happy": 'rate="medium" pitch="+8%"',
            "sad": 'rate="slow" pitch="-10%"',
            "angry": 'rate="fast" pitch="+5%" volume="+10%"',
            "excited": 'rate="fast" pitch="+15%"',
            "scary": 'rate="slow" pitch="-20%"',
            "whisper": 'rate="slow" pitch="-5%" volume="-20%"',
            "shout": 'rate="fast" pitch="+20%" volume="+20%"',
            "calm": 'rate="slow" pitch="-5%"'
        }

        prosody = emotion_settings.get(emotion, emotion_settings["neutral"])
        return f'<speak><prosody {prosody}>{text}</prosody></speak>'

    def split_long_text(self, text: str, max_words: int = None) -> List[str]:
        """
        Uzun metni küçük parçalara böl (50.000 kelime desteği için)

        Args:
            text: Bölünecek metin
            max_words: Maksimum kelime sayısı (varsayılan: self.max_chunk_words)

        Returns:
            Metin parçaları listesi
        """
        if max_words is None:
            max_words = self.max_chunk_words

        words = text.split()
        if len(words) <= max_words:
            return [text]

        chunks = []
        i = 0

        while i < len(words):
            # Chunk boyutunu hesapla
            end_idx = min(i + max_words, len(words))
            chunk_words = words[i:end_idx]

            # Cümle sınırında bitirmeye çalış
            if end_idx < len(words):
                # Son kelimeden geriye doğru noktalama ara
                for j in range(len(chunk_words) - 1, max(0, len(chunk_words) - 20), -1):
                    if chunk_words[j].endswith(('.', '!', '?', '...', ';')):
                        chunk_words = chunk_words[:j + 1]
                        end_idx = i + j + 1
                        break

            chunk_text = ' '.join(chunk_words)
            chunks.append(chunk_text)

            # Örtüşme ile devam et
            i = end_idx - self.overlap_words if end_idx < len(words) else end_idx

        self.logger.info(f"Uzun metin {len(chunks)} parçaya bölündü (toplam {len(words)} kelime)")
        return chunks

    def process_dynamic_pause(self, text: str) -> str:
        """
        Dinamik bekleme kodlarını işle (<#0.5#> formatı)

        Args:
            text: İşlenecek metin

        Returns:
            İşlenmiş metin
        """
        # <#sayı#> formatındaki kodları bul ve çevir
        pattern = r'<#(\d+(?:\.\d+)?)#>'

        def replace_pause(match):
            duration = float(match.group(1))
            ms = int(duration * 1000)
            return f'<break time="{ms}ms"/>'

        return re.sub(pattern, replace_pause, text)

    def estimate_speech_duration(self, text: str, words_per_minute: int = 150) -> float:
        """
        Konuşma süresini tahmin et

        Args:
            text: Metin
            words_per_minute: Dakikadaki kelime sayısı

        Returns:
            Tahmini süre (saniye)
        """
        words = len(text.split())
        minutes = words / words_per_minute
        return minutes * 60


# Global instance
_text_processor_instance = None

def get_text_processor() -> TextProcessor:
    """Global TextProcessor instance'ını al"""
    global _text_processor_instance
    if _text_processor_instance is None:
        _text_processor_instance = TextProcessor()
    return _text_processor_instance

"""
RecLastTTS API Veri Modelleri
"""

from typing import Optional, List, Union
from pydantic import BaseModel, Field, validator


class TTSRequest(BaseModel):
    """TTS isteği modeli"""
    text: str = Field(..., min_length=1, max_length=500000, description="Çevrilecek metin (50.000 kelime desteklenir)")
    language: str = Field(default="tr", description="Dil kodu (tr, en)")
    voice: Optional[str] = Field(default=None, description="Ses klonu adı")
    speed: float = Field(default=1.0, ge=0.5, le=2.0, description="Konuşma hızı")
    emotion: str = Field(default="neutral", description="Duygu (neutral, happy, sad, angry, excited, scary, whisper, shout, calm)")
    enable_chunking: bool = Field(default=True, description="Uzun metinler için otomatik bölme")
    chunk_size: Optional[int] = Field(default=100, ge=50, le=200, description="<PERSON>k boyutu (kelime)")

    @validator('language')
    def validate_language(cls, v):
        allowed_languages = ['tr', 'en']
        if v not in allowed_languages:
            raise ValueError(f'Desteklenen diller: {allowed_languages}')
        return v

    @validator('emotion')
    def validate_emotion(cls, v):
        allowed_emotions = ['neutral', 'happy', 'sad', 'angry', 'excited', 'scary', 'whisper', 'shout', 'calm']
        if v not in allowed_emotions:
            raise ValueError(f'Desteklenen duygular: {allowed_emotions}')
        return v


class VoiceCloneRequest(BaseModel):
    """Ses klonlama isteği modeli"""
    text: str = Field(..., min_length=1, max_length=1000, description="Çevrilecek metin")
    language: str = Field(default="tr", description="Dil kodu (tr, en)")
    speed: float = Field(default=1.0, ge=0.5, le=2.0, description="Konuşma hızı")
    
    @validator('language')
    def validate_language(cls, v):
        allowed_languages = ['tr', 'en']
        if v not in allowed_languages:
            raise ValueError(f'Desteklenen diller: {allowed_languages}')
        return v


class VoiceCreateRequest(BaseModel):
    """Ses oluşturma isteği modeli"""
    name: str = Field(..., min_length=2, max_length=50, description="Ses adı")
    description: str = Field(default="", max_length=200, description="Açıklama")
    language: str = Field(default="tr", description="Dil kodu")
    
    @validator('name')
    def validate_name(cls, v):
        # Geçersiz karakterleri kontrol et
        import re
        if not re.match(r'^[a-zA-Z0-9_\-\s]+$', v):
            raise ValueError('Ses adı sadece harf, rakam, tire ve alt çizgi içerebilir')
        return v.strip()


class TTSResponse(BaseModel):
    """TTS yanıt modeli"""
    success: bool = Field(..., description="İşlem başarılı mı")
    message: str = Field(..., description="Durum mesajı")
    audio_duration: Optional[float] = Field(None, description="Ses süresi (saniye)")
    processing_time: Optional[float] = Field(None, description="İşlem süresi (saniye)")
    characters_count: Optional[int] = Field(None, description="Karakter sayısı")


class VoiceInfo(BaseModel):
    """Ses bilgisi modeli"""
    name: str = Field(..., description="Ses adı")
    description: str = Field(..., description="Açıklama")
    language: str = Field(..., description="Dil")
    created_at: str = Field(..., description="Oluşturulma tarihi")
    usage_count: int = Field(..., description="Kullanım sayısı")
    last_used: Optional[str] = Field(None, description="Son kullanım")
    reference_files_count: int = Field(..., description="Referans dosya sayısı")


class VoicesListResponse(BaseModel):
    """Sesler listesi yanıt modeli"""
    success: bool = Field(..., description="İşlem başarılı mı")
    voices: List[VoiceInfo] = Field(..., description="Ses listesi")
    total_count: int = Field(..., description="Toplam ses sayısı")


class HealthResponse(BaseModel):
    """Sistem durumu yanıt modeli"""
    status: str = Field(..., description="Sistem durumu")
    model_status: str = Field(..., description="Model durumu")
    device: str = Field(..., description="Cihaz tipi")
    gpu_available: bool = Field(..., description="GPU mevcut mu")
    gpu_memory_gb: float = Field(..., description="GPU bellek (GB)")
    supported_languages: List[str] = Field(..., description="Desteklenen diller")
    test_tts: bool = Field(..., description="TTS testi başarılı mı")
    uptime: Optional[float] = Field(None, description="Çalışma süresi (saniye)")


class ErrorResponse(BaseModel):
    """Hata yanıt modeli"""
    success: bool = Field(default=False, description="İşlem başarılı mı")
    error: str = Field(..., description="Hata mesajı")
    error_code: Optional[str] = Field(None, description="Hata kodu")
    details: Optional[dict] = Field(None, description="Hata detayları")


class StatusResponse(BaseModel):
    """Durum yanıt modeli"""
    success: bool = Field(..., description="İşlem başarılı mı")
    server_status: str = Field(..., description="Server durumu")
    model_loaded: bool = Field(..., description="Model yüklü mü")
    active_requests: int = Field(..., description="Aktif istek sayısı")
    total_requests: int = Field(..., description="Toplam istek sayısı")
    average_response_time: float = Field(..., description="Ortalama yanıt süresi")
    memory_usage: dict = Field(..., description="Bellek kullanımı")


class ConfigResponse(BaseModel):
    """Konfigürasyon yanıt modeli"""
    success: bool = Field(..., description="İşlem başarılı mı")
    config: dict = Field(..., description="Konfigürasyon")


class ModelInfo(BaseModel):
    """Model bilgisi modeli"""
    model_name: str = Field(..., description="Model adı")
    device: str = Field(..., description="Cihaz")
    is_loaded: bool = Field(..., description="Yüklü mü")
    supported_languages: List[str] = Field(..., description="Desteklenen diller")
    max_text_length: int = Field(..., description="Maksimum metin uzunluğu")
    sample_rate: int = Field(..., description="Örnekleme hızı")


class ModelInfoResponse(BaseModel):
    """Model bilgisi yanıt modeli"""
    success: bool = Field(..., description="İşlem başarılı mı")
    model_info: ModelInfo = Field(..., description="Model bilgileri")

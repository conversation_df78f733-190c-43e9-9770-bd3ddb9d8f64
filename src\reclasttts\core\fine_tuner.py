#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
XTTS v2 Fine-tuning Manager
Co<PERSON> TTS 0.22.0 uyumlu
"""

import os
import json
import shutil
import threading
from typing import Dict, List, Optional, Callable
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.youtube_downloader import get_youtube_extractor
from .config import get_config


class XTTSFineTuner:
    """XTTS v2 Fine-tuning yöneticisi"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.config = get_config()
        
        # Fine-tuning paths
        self.base_path = Path("./fine_tuning")
        self.dataset_path = self.base_path / "dataset"
        self.output_path = self.base_path / "output"
        self.models_path = self.base_path / "models"
        
        # Training state
        self.is_training = False
        self.training_progress = 0
        self.training_status = "idle"
        self.training_log = []
        
        # Callbacks
        self.progress_callback: Optional[Callable] = None
        self.status_callback: Optional[Callable] = None
        
        self._ensure_directories()
    
    def _ensure_directories(self):
        """Gerekli klasörleri oluştur"""
        for path in [self.base_path, self.dataset_path, self.output_path, self.models_path]:
            path.mkdir(parents=True, exist_ok=True)
    
    def set_callbacks(self, progress_callback: Callable = None, status_callback: Callable = None):
        """Callback fonksiyonlarını ayarla"""
        self.progress_callback = progress_callback
        self.status_callback = status_callback
    
    def prepare_dataset_from_youtube(self, youtube_urls: List[str], speaker_name: str = "youtube_speaker") -> Dict:
        """YouTube URL'lerinden veri seti hazırla"""
        try:
            self.logger.info("🎬 YouTube'dan veri seti hazırlanıyor...")
            self._update_status("downloading_youtube", "YouTube videolarından ses çıkarılıyor...")

            # YouTube extractor'ı al
            youtube_extractor = get_youtube_extractor()

            # Progress callback ayarla
            def youtube_progress(message, progress):
                self._update_status("downloading_youtube", message)
                self._update_progress(progress)

            youtube_extractor.set_progress_callback(youtube_progress)

            # YouTube'dan ses çıkar
            temp_audio_dir = self.base_path / "temp_youtube_audio"
            result = youtube_extractor.extract_audio_from_urls(
                youtube_urls, str(temp_audio_dir), speaker_name
            )

            if not result["success"]:
                return {"success": False, "error": result["error"]}

            # Çıkarılan ses dosyalarını dataset formatına çevir
            audio_files = result["segments"]
            dataset_result = self.prepare_dataset(audio_files, speaker_name)

            # Geçici dosyaları temizle
            if temp_audio_dir.exists():
                import shutil
                shutil.rmtree(temp_audio_dir)

            # Sonucu güncelle
            if dataset_result["success"]:
                dataset_result.update({
                    "youtube_videos": len(youtube_urls),
                    "total_duration_minutes": result["total_duration_minutes"],
                    "source": "youtube"
                })

            return dataset_result

        except Exception as e:
            self.logger.error(f"❌ YouTube veri seti hazırlama hatası: {e}")
            self._update_status("error", f"YouTube hatası: {e}")
            return {"success": False, "error": str(e)}

    def prepare_dataset(self, audio_files: List[str], speaker_name: str = "custom_speaker") -> Dict:
        """Veri setini hazırla"""
        try:
            self.logger.info("📊 Veri seti hazırlanıyor...")
            self._update_status("preparing_dataset", "Veri seti hazırlanıyor...")
            
            # Dataset klasörünü temizle
            if self.dataset_path.exists():
                shutil.rmtree(self.dataset_path)
            self.dataset_path.mkdir(parents=True)
            
            # Wavs klasörü oluştur
            wavs_path = self.dataset_path / "wavs"
            wavs_path.mkdir()
            
            # Audio dosyalarını kopyala ve işle
            processed_files = []
            metadata_lines = []
            
            for i, audio_file in enumerate(audio_files):
                if not os.path.exists(audio_file):
                    continue
                
                # Dosya adını normalize et
                filename = f"{speaker_name}_{i:04d}.wav"
                target_path = wavs_path / filename
                
                # Dosyayı kopyala
                shutil.copy2(audio_file, target_path)
                
                # Metadata için basit transkript (gerçek uygulamada ASR kullanılmalı)
                transcript = f"Bu {speaker_name} konuşmacısının {i+1}. ses örneğidir."
                # LJSpeech formatter formatı: audio_file|text|text (3 sütun gerekli)
                metadata_lines.append(f"wavs/{filename}|{transcript}|{transcript}")
                
                processed_files.append(str(target_path))
                
                # Progress güncelle
                progress = int((i + 1) / len(audio_files) * 30)  # %30'a kadar
                self._update_progress(progress)
            
            # Metadata.csv oluştur
            metadata_path = self.dataset_path / "metadata.csv"
            with open(metadata_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(metadata_lines))
            
            # Speakers.json oluştur
            speakers_data = {
                "speakers": {
                    speaker_name: processed_files
                }
            }
            speakers_path = self.dataset_path / "speakers.json"
            with open(speakers_path, 'w', encoding='utf-8') as f:
                json.dump(speakers_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ Veri seti hazırlandı: {len(processed_files)} dosya")
            self._update_status("dataset_ready", f"Veri seti hazır: {len(processed_files)} dosya")
            
            return {
                "success": True,
                "files_processed": len(processed_files),
                "dataset_path": str(self.dataset_path),
                "speaker_name": speaker_name
            }
            
        except Exception as e:
            self.logger.error(f"❌ Veri seti hazırlama hatası: {e}")
            self._update_status("error", f"Veri seti hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def start_fine_tuning(self,
                         speaker_name: str = "custom_speaker",
                         epochs: int = 100,
                         batch_size: int = 2,
                         learning_rate: float = 1e-5,
                         use_real_training: bool = False) -> Dict:
        """Fine-tuning'i başlat"""
        try:
            if self.is_training:
                return {"success": False, "error": "Zaten eğitim devam ediyor"}
            
            self.logger.info("🚀 Fine-tuning başlatılıyor...")
            self._update_status("starting", "Fine-tuning başlatılıyor...")
            
            # Training config oluştur
            config = self._create_training_config(speaker_name, epochs, batch_size, learning_rate)
            
            # Training'i thread'de başlat
            training_thread = threading.Thread(
                target=self._run_training,
                args=(config, use_real_training),
                daemon=True
            )
            training_thread.start()
            
            return {"success": True, "message": "Fine-tuning başlatıldı"}
            
        except Exception as e:
            self.logger.error(f"❌ Fine-tuning başlatma hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def _create_training_config(self, speaker_name: str, epochs: int, batch_size: int, lr: float) -> Dict:
        """Training konfigürasyonu oluştur (Coqui TTS 0.22.0 uyumlu)"""

        # XTTS v2 için gerçek fine-tuning config
        config = {
            "model": "xtts",
            "run_name": f"xtts_v2_finetune_{speaker_name}",
            "run_description": f"XTTS v2 fine-tuning for {speaker_name}",

            # Dataset config
            "datasets": [
                {
                    "name": "custom_dataset",
                    "path": str(self.dataset_path),
                    "meta_file_train": "metadata.csv",
                    "meta_file_val": "metadata.csv",
                    "language": "tr",
                    "formatter": "ljspeech"
                }
            ],

            # Model config - XTTS için optimize edilmiş
            "model_args": {
                "gpt_batch_size": batch_size,
                "enable_redaction": False,
                "kv_cache": True,
                "gpt_max_audio_len": 229376,
                "gpt_max_text_len": 400,
                "gpt_max_prompt_len": 70,
                "gpt_layers": 30,
                "gpt_n_model_channels": 1024,
                "gpt_n_heads": 16,
                "gpt_number_text_tokens": 255,
                "gpt_start_text_token": 255,
                "gpt_checkpointing": True,
                "gpt_loss_text_ce_weight": 0.01,
                "gpt_loss_mel_ce_weight": 1.0,
                "gpt_use_masking_gt_prompt_approach": True,
                "gpt_use_mup": True
            },

            # Fine-tuning için restore path
            "restore_path": "tts_models/multilingual/multi-dataset/xtts_v2",

            # Training config
            "batch_size": batch_size,
            "eval_batch_size": batch_size,
            "num_loader_workers": 0,
            "num_eval_loader_workers": 0,
            "run_eval": True,
            "test_delay_epochs": -1,

            # Optimizer config
            "epochs": epochs,
            "lr": lr,
            "optimizer": "AdamW",
            "optimizer_params": {
                "betas": [0.9, 0.96],
                "eps": 1e-8,
                "weight_decay": 1e-2
            },

            # Scheduler config
            "lr_scheduler": "MultiStepLR",
            "lr_scheduler_params": {
                "milestones": [50000, 150000, 300000],
                "gamma": 0.5,
                "last_epoch": -1
            },

            # Logging and saving
            "log_model_step": 1000,
            "save_step": 1000,
            "save_n_checkpoints": 1,
            "save_checkpoints": True,
            "print_step": 25,
            "tb_model_param_stats": False,

            # Audio config
            "audio": {
                "sample_rate": 22050,
                "output_sample_rate": 24000,
            },

            # Output config
            "output_path": str(self.output_path),

            # GPU config
            "use_cuda": True,
            "mixed_precision": False,

            # Fine-tuning specific
            "restore_path": None,  # Will be set during training
            "strict_restore": False,
            "continue_path": None
        }

        # Config dosyasını kaydet
        config_path = self.base_path / "training_config.json"
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)

        self.logger.info(f"✅ Training config oluşturuldu: {config_path}")
        return config
    
    def _run_training(self, config: Dict, use_real_training: bool = False):
        """Training'i çalıştır"""
        try:
            self.is_training = True
            self._update_status("training", "Model eğitiliyor...")

            if use_real_training:
                # Gerçek Coqui TTS trainer kullan
                from ..utils.coqui_trainer import create_coqui_trainer
                trainer = create_coqui_trainer(str(self.dataset_path), str(self.output_path))
                self.logger.info("🚀 Gerçek Coqui TTS fine-tuning başlatılıyor...")
            else:
                # Simülasyon trainer kullan
                from ..utils.xtts_trainer import create_xtts_trainer
                trainer = create_xtts_trainer(str(self.dataset_path), str(self.output_path))
                self.logger.info("🎭 Simülasyon fine-tuning başlatılıyor...")

            # Progress callback ayarla
            def training_progress(message, progress):
                self._update_status("training", message)
                self._update_progress(progress)

            trainer.set_progress_callback(training_progress)

            # Fine-tuning'i çalıştır
            result = trainer.run_full_pipeline(
                epochs=config.get('epochs', 100),
                batch_size=config.get('batch_size', 2),
                lr=config.get('lr', 1e-5)
            )

            if result["success"]:
                self.logger.info("✅ Fine-tuning başarıyla tamamlandı")
                self._update_status("completed", "Fine-tuning tamamlandı")
                self._update_progress(100)
                return
            else:
                raise Exception(result["error"])
                
        except Exception as e:
            self.logger.error(f"❌ Training hatası: {e}")
            self._update_status("error", f"Training hatası: {e}")
        finally:
            self.is_training = False
    
    def _parse_training_log(self, line: str):
        """Training log'unu parse et ve progress güncelle"""
        try:
            # Epoch progress
            if "Epoch:" in line and "/" in line:
                # Örnek: "Epoch: 15/100"
                parts = line.split("Epoch:")[-1].strip().split("/")
                if len(parts) == 2:
                    current = int(parts[0].strip())
                    total = int(parts[1].strip())
                    progress = int((current / total) * 70) + 30  # %30-100 arası
                    self._update_progress(progress)
            
            # Loss bilgisi
            if "loss:" in line.lower():
                self._update_status("training", f"Eğitim devam ediyor... {line.strip()}")
                
        except Exception:
            pass  # Log parsing hatalarını görmezden gel
    
    def _update_progress(self, progress: int):
        """Progress güncelle"""
        self.training_progress = min(100, max(0, progress))
        if self.progress_callback:
            self.progress_callback(self.training_progress)
    
    def _update_status(self, status: str, message: str):
        """Status güncelle"""
        self.training_status = status
        if self.status_callback:
            self.status_callback(status, message)
    
    def get_status(self) -> Dict:
        """Mevcut durumu al"""
        return {
            "is_training": self.is_training,
            "progress": self.training_progress,
            "status": self.training_status,
            "log_lines": len(self.training_log),
            "dataset_ready": (self.dataset_path / "metadata.csv").exists(),
            "model_ready": (self.output_path / "best_model.pth").exists()
        }
    
    def get_training_log(self, last_n: int = 50) -> List[str]:
        """Training log'unu al"""
        return self.training_log[-last_n:] if self.training_log else []
    
    def stop_training(self) -> Dict:
        """Training'i durdur"""
        try:
            if not self.is_training:
                return {"success": False, "error": "Aktif eğitim yok"}
            
            # Training'i durdurmak için flag set et
            self.is_training = False
            self._update_status("stopped", "Eğitim durduruldu")
            
            return {"success": True, "message": "Eğitim durduruldu"}
            
        except Exception as e:
            return {"success": False, "error": str(e)}


# Global instance
_fine_tuner = None

def get_fine_tuner() -> XTTSFineTuner:
    """Global fine-tuner instance'ını al"""
    global _fine_tuner
    if _fine_tuner is None:
        _fine_tuner = XTTSFineTuner()
    return _fine_tuner

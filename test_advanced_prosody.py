#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Prosody Test
Gelişmiş prosodi sistemi testi
"""

import os
import time
from src.reclasttts.core.tts_engine import RecLastTTSEngine
from src.reclasttts.core.turkish_prosody_engine import get_turkish_prosody_engine
from src.reclasttts.core.xtts_prosody_controller import get_xtts_prosody_controller


def test_turkish_prosody():
    """Türkçe prosodi testi"""
    print("\n🎵 Türkçe Prosodi Testi")
    print("=" * 50)
    
    prosody_engine = get_turkish_prosody_engine()
    
    # Türkçe prosodi testleri
    prosody_tests = [
        {
            "text": "Bu kitabı okuyorum. Çok güzel bir hikaye.",
            "description": "Basit cümle prosodisi",
            "expected": "Nokta sonrası doğal duraklama"
        },
        {
            "text": "Nereye gidiyorsun? <PERSON><PERSON> de gö<PERSON>ür mü?",
            "description": "Soru cümleleri prosodisi",
            "expected": "Soru tonlaması ve yükselen ezgi"
        },
        {
            "text": "Gelmiyor musun? Gelmez mi acaba?",
            "description": "Olumsuz soru prosodisi",
            "expected": "Karmaşık soru tonlaması"
        },
        {
            "text": "Çok güzel, harika, mükemmel bir gün!",
            "description": "Virgül ve ünlem prosodisi",
            "expected": "Virgül duraklamaları ve ünlem vurgusu"
        },
        {
            "text": "Belki gelir... Belki de gelmez.",
            "description": "Üç nokta prosodisi",
            "expected": "Uzun duraklama ve düşen ton"
        }
    ]
    
    for i, test in enumerate(prosody_tests, 1):
        print(f"\n📝 Test {i} - {test['description']}:")
        print(f"Orijinal: {test['text']}")
        print(f"Beklenen: {test['expected']}")
        
        # Prosodi işleme
        processed = prosody_engine.process_turkish_prosody(test['text'])
        print(f"Prosodi: {processed}")
        
        # Prosodi özellikleri
        features = prosody_engine.extract_prosody_features(test['text'])
        print(f"Özellikler: {features}")


def test_xtts_prosody_control():
    """XTTS prosodi kontrolü testi"""
    print("\n🎛️ XTTS Prosodi Kontrolü Testi")
    print("=" * 50)
    
    prosody_controller = get_xtts_prosody_controller()
    
    # Base parametreler
    base_params = {
        'temperature': 0.7,
        'repetition_penalty': 5.0,
        'top_p': 0.8,
        'top_k': 50
    }
    
    # Prosodi modifikasyonları
    prosody_modifications = [
        {
            'name': 'Yüksek Pitch',
            'params': {'pitch_mod': 0.3, 'duration_mod': 1.0, 'energy_mod': 0.1}
        },
        {
            'name': 'Düşük Pitch',
            'params': {'pitch_mod': -0.2, 'duration_mod': 1.0, 'energy_mod': -0.1}
        },
        {
            'name': 'Hızlı Konuşma',
            'params': {'pitch_mod': 0.0, 'duration_mod': 0.8, 'energy_mod': 0.2}
        },
        {
            'name': 'Yavaş Konuşma',
            'params': {'pitch_mod': 0.0, 'duration_mod': 1.3, 'energy_mod': -0.1}
        },
        {
            'name': 'Yüksek Enerji',
            'params': {'pitch_mod': 0.1, 'duration_mod': 1.0, 'energy_mod': 0.4}
        }
    ]
    
    for mod in prosody_modifications:
        print(f"\n📝 {mod['name']}:")
        print(f"Prosodi Params: {mod['params']}")
        
        # Parametreleri uygula
        modified_params = prosody_controller.apply_prosody_to_generation(
            "Test metni", mod['params'], base_params.copy()
        )
        
        print(f"Base Params: {base_params}")
        print(f"Modified Params: {modified_params}")
        
        # Değişiklikleri göster
        for key in base_params:
            if key in modified_params:
                change = modified_params[key] - base_params[key]
                print(f"  {key}: {base_params[key]} -> {modified_params[key]} (Δ{change:+.2f})")


def test_advanced_tts():
    """Gelişmiş TTS testi"""
    print("\n🚀 Gelişmiş TTS Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Gelişmiş prosodi testleri
    advanced_tests = [
        {
            "text": "Merhaba! Nasılsın? Ben iyiyim, teşekkürler.",
            "emotion": "happy",
            "description": "Mutlu selamlama prosodisi"
        },
        {
            "text": "Bu çok önemli bir konu. Dikkatli dinleyin lütfen.",
            "emotion": "neutral",
            "description": "Ciddi açıklama prosodisi"
        },
        {
            "text": "Gerçekten mi? İnanamıyorum! Bu harika bir haber.",
            "emotion": "excited",
            "description": "Şaşkınlık ve heyecan prosodisi"
        },
        {
            "text": "Üzgünüm... Bu durumda yapabileceğim bir şey yok.",
            "emotion": "sad",
            "description": "Üzgün açıklama prosodisi"
        },
        {
            "text": "Dikkat! Tehlikeli bir durum var. Hemen uzaklaşın.",
            "emotion": "scary",
            "description": "Uyarı prosodisi"
        }
    ]
    
    for i, test in enumerate(advanced_tests, 1):
        print(f"\n📝 Test {i} - {test['description']}:")
        print(f"Metin: {test['text']}")
        print(f"Duygu: {test['emotion']}")
        
        # TTS ile test
        start_time = time.time()
        audio_data = engine.text_to_speech(
            text=test['text'],
            language="tr",
            emotion=test['emotion']
        )
        end_time = time.time()
        
        if audio_data is not None:
            output_path = f"output/advanced_prosody_{i}.wav"
            os.makedirs("output", exist_ok=True)
            success = engine.save_audio(audio_data, output_path)
            
            if success:
                duration = len(audio_data) / 22050
                print(f"✅ TTS Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
                print(f"📁 Kaydedildi: {output_path}")
                print(f"🎯 Kontrol: {test['description']}")
            else:
                print("❌ Ses kaydedilemedi")
        else:
            print("❌ TTS işlemi başarısız")


def test_comprehensive_prosody():
    """Kapsamlı prosodi testi"""
    print("\n🎬 Kapsamlı Prosodi Testi")
    print("=" * 50)
    
    engine = RecLastTTSEngine()
    
    # Tüm prosodi özelliklerini içeren test
    comprehensive_text = """
    Merhaba arkadaşlar! Bugün çok özel bir gün.
    
    Size önemli bir haber vermek istiyorum. Hazır mısınız?
    
    RecLastTTS sistemimiz artık gelişmiş prosodi desteğine sahip!
    
    Bu ne demek? Şimdi açıklayayım:
    
    Birincisi, noktalama işaretleri doğal duraklamalar yapıyor.
    İkincisi, soru cümleleri yükselen tonla okunuyor.
    Üçüncüsü, ünlem cümleleri vurgulu şekilde ifade ediliyor!
    
    Peki ya fiiller? Onlar da özel prosodi kurallarıyla işleniyor.
    
    Gelmiyor musun? Gelmez mi acaba? Bu tür sorular artık doğal.
    
    Harika değil mi? Ben çok memnunum... Siz de öyle olacaksınız.
    
    Teşekkürler!
    """
    
    print("📝 Kapsamlı prosodi test metni hazırlandı")
    print(f"📊 Metin uzunluğu: {len(comprehensive_text)} karakter")
    
    start_time = time.time()
    audio_data = engine.text_to_speech(
        text=comprehensive_text,
        language="tr",
        emotion="neutral",
        enable_chunking=True,
        chunk_size=30
    )
    end_time = time.time()
    
    if audio_data is not None:
        output_path = "output/comprehensive_prosody.wav"
        os.makedirs("output", exist_ok=True)
        success = engine.save_audio(audio_data, output_path)
        
        if success:
            duration = len(audio_data) / 22050
            word_count = len(comprehensive_text.split())
            print(f"✅ Başarılı: {end_time - start_time:.2f}s işlem, {duration:.2f}s ses")
            print(f"📁 Kaydedildi: {output_path}")
            print(f"📊 {word_count} kelime, {word_count / (end_time - start_time):.1f} kelime/saniye")
            print(f"🎯 Gelişmiş prosodi kalitesini kontrol edin!")
        else:
            print("❌ Ses kaydedilemedi")
    else:
        print("❌ TTS işlemi başarısız")


def main():
    """Ana test fonksiyonu"""
    print("🎙️ RecLastTTS Advanced Prosody Test")
    print("=" * 60)
    print("🎯 Gelişmiş prosodi sistemi test ediliyor...")
    print("\n📚 Araştırma Temelli Yaklaşım:")
    print("• Baran Uslu'nun Türkçe TTS prosodi araştırması")
    print("• XTTS v2 parametrik kontrol optimizasyonu")
    print("• Türkçe fiil çekim prosodi kuralları")
    print("• Noktalama tabanlı ezgi kontrolü")
    
    try:
        # Testleri çalıştır
        test_turkish_prosody()
        test_xtts_prosody_control()
        test_advanced_tts()
        test_comprehensive_prosody()
        
        print("\n🎉 Gelişmiş prosodi testleri tamamlandı!")
        print("📁 Ses dosyaları 'output/' klasöründe kaydedildi.")
        print("\n📋 Gelişmiş Özellikler:")
        print("✅ Türkçe fiil prosodi kuralları")
        print("✅ Noktalama tabanlı ezgi kontrolü")
        print("✅ XTTS v2 parametrik optimizasyon")
        print("✅ Duygu bazında prosodi modifikasyonu")
        print("✅ Araştırma temelli yaklaşım")
        print("\n🎧 Ses dosyalarını dinleyerek gelişmiş prosodi kalitesini kontrol edin!")
        print("\n⚠️ Özellikle kontrol edilecekler:")
        print("1. Noktalarda doğal düşen ton var mı?")
        print("2. Soru cümlelerinde yükselen ezgi var mı?")
        print("3. Virgüllerde kısa duraklamalar var mı?")
        print("4. Fiil çekimlerinde doğal vurgu var mı?")
        print("5. Genel konuşma akıcılığı nasıl?")
        print("\n🚀 Bu yaklaşım daha doğal mı?")
        
    except Exception as e:
        print(f"❌ Test hatası: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()

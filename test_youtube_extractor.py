#!/usr/bin/env python3
"""YouTube Audio Extractor Test"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from reclasttts.utils.youtube_downloader import get_youtube_extractor

def test_youtube_extractor():
    print("🧪 YouTube Audio Extractor testi...")
    
    try:
        extractor = get_youtube_extractor()
        print("✅ YouTube extractor başarıyla oluşturuldu")
        
        # URL validation testi
        test_urls = [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "invalid_url"
        ]
        
        for url in test_urls:
            is_valid = extractor.validate_youtube_url(url)
            print(f"URL: {url[:50]}... -> {'✅ Geçerli' if is_valid else '❌ Geçersiz'}")
        
        print("🎉 YouTube extractor testi başarılı!")
        return True
        
    except Exception as e:
        print(f"❌ YouTube extractor testi başarısız: {e}")
        return False

if __name__ == "__main__":
    test_youtube_extractor()

{"model": "xtts", "run_name": "xtts_v2_finetune_custom_speaker", "run_description": "XTTS v2 fine-tuning for custom_speaker", "dataset": "fine_tuning\\dataset", "meta_file_train": "fine_tuning\\dataset\\metadata.csv", "meta_file_val": "fine_tuning\\dataset\\metadata.csv", "language": "tr", "formatter": "l<PERSON><PERSON><PERSON><PERSON>", "model_args": {"gpt_batch_size": 2, "enable_redaction": false, "kv_cache": true, "gpt_max_audio_len": 229376, "gpt_max_text_len": 400, "gpt_max_prompt_len": 70, "gpt_layers": 30, "gpt_n_model_channels": 1024, "gpt_n_heads": 16, "gpt_number_text_tokens": 255, "gpt_start_text_token": 255, "gpt_checkpointing": true, "gpt_loss_text_ce_weight": 0.01, "gpt_loss_mel_ce_weight": 1.0, "gpt_use_masking_gt_prompt_approach": true, "gpt_use_mup": true}, "batch_size": 2, "eval_batch_size": 2, "num_loader_workers": 0, "num_eval_loader_workers": 0, "run_eval": true, "test_delay_epochs": -1, "epochs": 100, "lr": 1e-05, "optimizer": "AdamW", "optimizer_params": {"betas": [0.9, 0.96], "eps": 1e-08, "weight_decay": 0.01}, "lr_scheduler": "MultiStepLR", "lr_scheduler_params": {"milestones": [50000, 150000, 300000], "gamma": 0.5, "last_epoch": -1}, "log_model_step": 1000, "save_step": 1000, "save_n_checkpoints": 1, "save_checkpoints": true, "print_step": 25, "tb_model_param_stats": false, "audio": {"sample_rate": 22050, "output_sample_rate": 24000}, "output_path": "fine_tuning\\output", "use_cuda": true, "mixed_precision": false, "restore_path": null, "strict_restore": false, "continue_path": null}
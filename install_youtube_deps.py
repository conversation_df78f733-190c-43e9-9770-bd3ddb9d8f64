#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube Dependencies Installer
YouTube fine-tuning için gerek<PERSON> bağımlılıkları yükle
"""

import subprocess
import sys
import os
from pathlib import Path


def run_command(cmd, description):
    """Komutu çalıştır ve sonucu göster"""
    print(f"\n🔧 {description}...")
    try:
        result = subprocess.run(cmd, shell=True, check=True, 
                              capture_output=True, text=True)
        print(f"✅ {description} başarılı")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} başarısız: {e}")
        if e.stdout:
            print(f"STDOUT: {e.stdout}")
        if e.stderr:
            print(f"STDERR: {e.stderr}")
        return False


def install_python_packages():
    """Python paketlerini yükle"""
    packages = [
        "yt-dlp",           # YouTube downloader
        "librosa",          # Audio processing
        "soundfile",        # Audio file I/O
        "numpy",            # Numerical computing
    ]
    
    for package in packages:
        success = run_command(
            f"pip install {package}",
            f"{package} yükleniyor"
        )
        if not success:
            print(f"⚠️ {package} yüklenemedi, devam ediliyor...")


def install_ffmpeg():
    """FFmpeg'i yükle"""
    print("\n🎬 FFmpeg kurulumu...")
    
    # FFmpeg'in zaten yüklü olup olmadığını kontrol et
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg zaten yüklü")
            return True
    except FileNotFoundError:
        pass
    
    print("📥 FFmpeg indiriliyor...")
    
    # Windows için FFmpeg indir
    if os.name == 'nt':
        print("🪟 Windows için FFmpeg indiriliyor...")
        
        # FFmpeg binary'sini indir ve çıkar
        ffmpeg_url = "https://github.com/BtbN/FFmpeg-Builds/releases/download/latest/ffmpeg-master-latest-win64-gpl.zip"
        
        commands = [
            f"curl -L -o ffmpeg.zip {ffmpeg_url}",
            "powershell -command \"Expand-Archive -Path ffmpeg.zip -DestinationPath .\"",
            "move ffmpeg-master-latest-win64-gpl\\bin\\ffmpeg.exe .",
            "move ffmpeg-master-latest-win64-gpl\\bin\\ffprobe.exe .",
            "rmdir /s /q ffmpeg-master-latest-win64-gpl",
            "del ffmpeg.zip"
        ]
        
        for cmd in commands:
            run_command(cmd, f"FFmpeg kurulum adımı: {cmd[:50]}...")
        
        # PATH'e ekle (geçici)
        current_dir = os.getcwd()
        os.environ['PATH'] = current_dir + os.pathsep + os.environ['PATH']
        
        print("✅ FFmpeg Windows'a kuruldu")
        
    else:
        # Linux/Mac için
        print("🐧 Linux/Mac için FFmpeg yükleniyor...")
        
        # Farklı package manager'ları dene
        managers = [
            ("apt-get update && apt-get install -y ffmpeg", "apt-get"),
            ("yum install -y ffmpeg", "yum"),
            ("brew install ffmpeg", "brew"),
            ("pacman -S --noconfirm ffmpeg", "pacman")
        ]
        
        for cmd, manager in managers:
            if run_command(f"which {manager.split()[0]}", f"{manager} kontrolü"):
                if run_command(cmd, f"FFmpeg {manager} ile yükleniyor"):
                    break
        else:
            print("⚠️ FFmpeg otomatik yüklenemedi. Manuel yükleme gerekebilir.")
    
    # Kurulum kontrolü
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg başarıyla kuruldu")
            return True
    except FileNotFoundError:
        pass
    
    print("❌ FFmpeg kurulumu başarısız")
    return False


def test_installation():
    """Kurulumu test et"""
    print("\n🧪 Kurulum testi...")
    
    tests = [
        ("python -c \"import yt_dlp; print('yt-dlp:', yt_dlp.version.__version__)\"", "yt-dlp"),
        ("python -c \"import librosa; print('librosa:', librosa.__version__)\"", "librosa"),
        ("python -c \"import soundfile; print('soundfile:', soundfile.__version__)\"", "soundfile"),
        ("ffmpeg -version", "ffmpeg"),
        ("yt-dlp --version", "yt-dlp command")
    ]
    
    success_count = 0
    for cmd, name in tests:
        if run_command(cmd, f"{name} testi"):
            success_count += 1
    
    print(f"\n📊 Test sonucu: {success_count}/{len(tests)} başarılı")
    
    if success_count == len(tests):
        print("🎉 Tüm bağımlılıklar başarıyla kuruldu!")
        return True
    else:
        print("⚠️ Bazı bağımlılıklar eksik. Manuel kurulum gerekebilir.")
        return False


def create_test_script():
    """Test scripti oluştur"""
    test_script = '''#!/usr/bin/env python3
"""YouTube Audio Extractor Test"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from reclasttts.utils.youtube_downloader import get_youtube_extractor

def test_youtube_extractor():
    print("🧪 YouTube Audio Extractor testi...")
    
    try:
        extractor = get_youtube_extractor()
        print("✅ YouTube extractor başarıyla oluşturuldu")
        
        # URL validation testi
        test_urls = [
            "https://www.youtube.com/watch?v=dQw4w9WgXcQ",
            "https://youtu.be/dQw4w9WgXcQ",
            "invalid_url"
        ]
        
        for url in test_urls:
            is_valid = extractor.validate_youtube_url(url)
            print(f"URL: {url[:50]}... -> {'✅ Geçerli' if is_valid else '❌ Geçersiz'}")
        
        print("🎉 YouTube extractor testi başarılı!")
        return True
        
    except Exception as e:
        print(f"❌ YouTube extractor testi başarısız: {e}")
        return False

if __name__ == "__main__":
    test_youtube_extractor()
'''
    
    with open("test_youtube_extractor.py", "w", encoding="utf-8") as f:
        f.write(test_script)
    
    print("📝 Test scripti oluşturuldu: test_youtube_extractor.py")


def main():
    """Ana kurulum fonksiyonu"""
    print("🎬 YouTube Fine-tuning Dependencies Installer")
    print("=" * 50)
    print("Bu script YouTube videolarından ses çıkarmak için")
    print("gerekli bağımlılıkları yükleyecek.")
    print()
    
    # Python paketlerini yükle
    install_python_packages()
    
    # FFmpeg'i yükle
    install_ffmpeg()
    
    # Kurulumu test et
    test_installation()
    
    # Test scripti oluştur
    create_test_script()
    
    print("\n🎯 Kurulum tamamlandı!")
    print("\nSonraki adımlar:")
    print("1. Web UI'yi başlatın: python run_ui.py")
    print("2. Fine-tuning tab'ına gidin")
    print("3. YouTube URL'lerinizi ekleyin")
    print("4. Ses çıkarma işlemini başlatın")
    print("\n🎉 İyi fine-tuning'ler!")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dataset Builder for Real Fine-tuning
Gerçek fine-tuning için dataset hazırlama
"""

import os
import csv
import json
import shutil
from pathlib import Path
from typing import List, Dict, Optional
import librosa
import soundfile as sf

from .logger import get_logger


class RealDatasetBuilder:
    """Gerçek fine-tuning için dataset builder"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.target_sample_rate = 22050
        self.min_duration = 1.0  # Minimum 1 saniye
        self.max_duration = 15.0  # Maximum 15 saniye
        self.min_text_length = 10  # Minimum 10 karakter
        self.max_text_length = 200  # Maximum 200 karakter
    
    def build_dataset_from_youtube_data(self, youtube_result: Dict, output_dir: str) -> Dict:
        """YouTube verilerinden dataset oluştur"""
        try:
            self.logger.info("📊 Gerçek fine-tuning dataset'i hazırlanıyor...")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Wavs klasörü
            wavs_dir = output_path / "wavs"
            wavs_dir.mkdir(exist_ok=True)
            
            # Transkript verilerini al
            transcripts = youtube_result.get('transcripts', [])
            segments = youtube_result.get('segments', [])
            speaker_name = youtube_result.get('speaker_name', 'speaker')
            
            if not transcripts:
                self.logger.warning("⚠️ Transkript verisi bulunamadı, ses segmentlerini kullanıyor...")
                return self._build_from_audio_segments(segments, output_path, speaker_name)
            
            # Transkript verilerinden dataset oluştur
            return self._build_from_transcripts(transcripts, segments, output_path, speaker_name)
            
        except Exception as e:
            self.logger.error(f"❌ Dataset oluşturma hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def _build_from_transcripts(self, transcripts: List[Dict], segments: List, 
                              output_path: Path, speaker_name: str) -> Dict:
        """Transkriptlerden dataset oluştur"""
        try:
            wavs_dir = output_path / "wavs"
            metadata_entries = []
            processed_count = 0
            
            self.logger.info(f"📝 {len(transcripts)} transkript segmenti işleniyor...")
            
            for i, transcript in enumerate(transcripts):
                text = transcript.get('text', '').strip()
                video_id = transcript.get('video_id', '')
                start_time = transcript.get('start_time', 0)
                duration = transcript.get('duration', 3.0)
                
                # Metin kontrolü
                if not self._is_valid_text(text):
                    continue
                
                # Süre kontrolü
                if not self._is_valid_duration(duration):
                    continue
                
                # Ses dosyasını bul (eğer varsa)
                audio_file = self._find_matching_audio_segment(segments, start_time, duration)
                
                if not audio_file:
                    # Ses dosyası yoksa atla
                    continue
                
                # Ses dosyasını kopyala ve yeniden adlandır
                target_filename = f"{speaker_name}_{processed_count:04d}.wav"
                target_path = wavs_dir / target_filename
                
                try:
                    # Ses dosyasını işle ve kaydet
                    if self._process_and_save_audio(audio_file, target_path, duration):
                        # Metadata girişi oluştur
                        metadata_entries.append({
                            'audio_file': f"wavs/{target_filename}",
                            'text': text,
                            'speaker': speaker_name,
                            'duration': duration,
                            'video_id': video_id,
                            'start_time': start_time
                        })
                        processed_count += 1
                        
                        if processed_count % 50 == 0:
                            self.logger.info(f"📊 {processed_count} segment işlendi...")
                    
                except Exception as e:
                    self.logger.warning(f"Segment {i} işleme hatası: {e}")
                    continue
            
            # Metadata dosyasını kaydet
            self._save_metadata(metadata_entries, output_path)
            
            # Speakers.json dosyasını kaydet
            self._save_speakers_config(speaker_name, output_path)
            
            self.logger.info(f"✅ Dataset hazırlandı: {processed_count} dosya")
            
            return {
                "success": True,
                "total_files": processed_count,
                "dataset_path": str(output_path),
                "metadata_file": str(output_path / "metadata.csv"),
                "speakers_file": str(output_path / "speakers.json"),
                "speaker_name": speaker_name,
                "type": "real_transcript_dataset"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Transkript dataset oluşturma hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def _build_from_audio_segments(self, segments: List, output_path: Path, speaker_name: str) -> Dict:
        """Ses segmentlerinden dataset oluştur (transkript olmadan)"""
        try:
            wavs_dir = output_path / "wavs"
            metadata_entries = []
            processed_count = 0
            
            self.logger.info(f"🎵 {len(segments)} ses segmenti işleniyor...")
            
            for i, segment in enumerate(segments):
                if isinstance(segment, dict):
                    audio_file = segment.get('audio_file')
                    text = segment.get('text', f"Bu {speaker_name} konuşmacısının {i+1}. ses örneğidir.")
                    duration = segment.get('duration', 3.0)
                else:
                    audio_file = segment
                    text = f"Bu {speaker_name} konuşmacısının {i+1}. ses örneğidir."
                    duration = 3.0
                
                if not audio_file or not os.path.exists(audio_file):
                    continue
                
                # Süre kontrolü
                if not self._is_valid_duration(duration):
                    continue
                
                # Hedef dosya adı
                target_filename = f"{speaker_name}_{processed_count:04d}.wav"
                target_path = wavs_dir / target_filename
                
                try:
                    # Ses dosyasını işle ve kaydet
                    if self._process_and_save_audio(audio_file, target_path, duration):
                        # Metadata girişi oluştur
                        metadata_entries.append({
                            'audio_file': f"wavs/{target_filename}",
                            'text': text,
                            'speaker': speaker_name,
                            'duration': duration
                        })
                        processed_count += 1
                        
                        if processed_count % 50 == 0:
                            self.logger.info(f"📊 {processed_count} segment işlendi...")
                
                except Exception as e:
                    self.logger.warning(f"Segment {i} işleme hatası: {e}")
                    continue
            
            # Metadata dosyasını kaydet
            self._save_metadata(metadata_entries, output_path)
            
            # Speakers.json dosyasını kaydet
            self._save_speakers_config(speaker_name, output_path)
            
            self.logger.info(f"✅ Dataset hazırlandı: {processed_count} dosya")
            
            return {
                "success": True,
                "total_files": processed_count,
                "dataset_path": str(output_path),
                "metadata_file": str(output_path / "metadata.csv"),
                "speakers_file": str(output_path / "speakers.json"),
                "speaker_name": speaker_name,
                "type": "audio_segment_dataset"
            }
            
        except Exception as e:
            self.logger.error(f"❌ Ses segment dataset oluşturma hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def _is_valid_text(self, text: str) -> bool:
        """Metin geçerli mi kontrol et"""
        if not text or len(text) < self.min_text_length or len(text) > self.max_text_length:
            return False
        
        # Sadece sayı veya özel karakter içeren metinleri reddet
        if text.isdigit() or not any(c.isalpha() for c in text):
            return False
        
        return True
    
    def _is_valid_duration(self, duration: float) -> bool:
        """Süre geçerli mi kontrol et"""
        return self.min_duration <= duration <= self.max_duration
    
    def _find_matching_audio_segment(self, segments: List, start_time: float, duration: float) -> Optional[str]:
        """Transkripte uygun ses segmentini bul"""
        try:
            # Segments listesinde eşleşen ses dosyasını ara
            for segment in segments:
                if isinstance(segment, dict):
                    segment_start = segment.get('start_time', 0)
                    segment_duration = segment.get('duration', 3.0)
                    audio_file = segment.get('audio_file')
                    
                    # Zaman aralığı kontrolü (±1 saniye tolerans)
                    if (abs(segment_start - start_time) <= 1.0 and 
                        abs(segment_duration - duration) <= 1.0 and
                        audio_file and os.path.exists(audio_file)):
                        return audio_file
            
            return None
            
        except Exception:
            return None
    
    def _process_and_save_audio(self, source_file: str, target_file: Path, target_duration: float) -> bool:
        """Ses dosyasını işle ve kaydet"""
        try:
            # Ses dosyasını yükle
            audio, sr = librosa.load(source_file, sr=self.target_sample_rate, mono=True)
            
            # Hedef uzunluğa göre kırp veya uzat
            target_samples = int(target_duration * sr)
            
            if len(audio) > target_samples:
                # Kırp
                audio = audio[:target_samples]
            elif len(audio) < target_samples:
                # Uzat (sessizlik ekle)
                padding = target_samples - len(audio)
                audio = np.pad(audio, (0, padding), mode='constant', constant_values=0)
            
            # Normalize et
            if np.max(np.abs(audio)) > 0:
                audio = audio / np.max(np.abs(audio)) * 0.95
            
            # Kaydet
            sf.write(target_file, audio, sr)
            return True
            
        except Exception as e:
            self.logger.error(f"Ses işleme hatası: {e}")
            return False
    
    def _save_metadata(self, metadata_entries: List[Dict], output_path: Path):
        """Metadata CSV dosyasını kaydet"""
        try:
            metadata_file = output_path / "metadata.csv"
            
            with open(metadata_file, 'w', newline='', encoding='utf-8') as f:
                for entry in metadata_entries:
                    audio_file = entry['audio_file']
                    text = entry['text']
                    # LJSpeech formatı: audio_file|text|text
                    f.write(f"{audio_file}|{text}|{text}\n")
            
            self.logger.info(f"✅ Metadata kaydedildi: {metadata_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Metadata kaydetme hatası: {e}")
    
    def _save_speakers_config(self, speaker_name: str, output_path: Path):
        """Speakers.json dosyasını kaydet"""
        try:
            speakers_file = output_path / "speakers.json"
            
            speakers_config = {
                "speakers": {
                    speaker_name: {
                        "name": speaker_name,
                        "language": "tr",
                        "dataset_path": str(output_path),
                        "created_by": "RecLastTTS"
                    }
                }
            }
            
            with open(speakers_file, 'w', encoding='utf-8') as f:
                json.dump(speakers_config, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ Speakers config kaydedildi: {speakers_file}")
            
        except Exception as e:
            self.logger.error(f"❌ Speakers config kaydetme hatası: {e}")


def get_dataset_builder() -> RealDatasetBuilder:
    """Global dataset builder instance"""
    return RealDatasetBuilder()

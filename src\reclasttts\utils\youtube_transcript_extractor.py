#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube Transcript Extractor
YouTube videolarından gerçek transkriptleri çıkarma
"""

import os
import re
import json
import subprocess
import tempfile
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from urllib.parse import urlparse, parse_qs

from .logger import get_logger

try:
    from youtube_transcript_api import YouTubeTranscriptApi
    from youtube_transcript_api.formatters import TextFormatter
    TRANSCRIPT_API_AVAILABLE = True
except ImportError:
    TRANSCRIPT_API_AVAILABLE = False


class YouTubeTranscriptExtractor:
    """YouTube transkript çıkarma sistemi"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self.formatter = TextFormatter() if TRANSCRIPT_API_AVAILABLE else None
        
        if not TRANSCRIPT_API_AVAILABLE:
            self.logger.warning("⚠️ youtube-transcript-api bulunamadı. pip install youtube-transcript-api")
    
    def extract_video_id(self, url: str) -> Optional[str]:
        """YouTube URL'sinden video ID'sini çıkar"""
        try:
            # YouTube URL formatları
            patterns = [
                r'(?:youtube\.com/watch\?v=|youtu\.be/)([a-zA-Z0-9_-]{11})',
                r'youtube\.com/embed/([a-zA-Z0-9_-]{11})',
                r'youtube\.com/v/([a-zA-Z0-9_-]{11})'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, url)
                if match:
                    return match.group(1)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Video ID çıkarma hatası: {e}")
            return None
    
    def get_transcript(self, video_id: str, languages: List[str] = ['tr', 'en']) -> Optional[List[Dict]]:
        """Video transkriptini al"""
        try:
            if not TRANSCRIPT_API_AVAILABLE:
                return self._get_transcript_with_yt_dlp(video_id)
            
            # YouTube Transcript API ile dene
            try:
                transcript_list = YouTubeTranscriptApi.list_transcripts(video_id)
                
                # Önce manuel transkriptleri dene
                for language in languages:
                    try:
                        transcript = transcript_list.find_manually_created_transcript([language])
                        raw_transcript = transcript.fetch()
                        # API formatını standart formata çevir
                        return [{'text': item['text'], 'start': item['start'], 'duration': item['duration']}
                               for item in raw_transcript]
                    except:
                        continue

                # Sonra otomatik transkriptleri dene
                for language in languages:
                    try:
                        transcript = transcript_list.find_generated_transcript([language])
                        raw_transcript = transcript.fetch()
                        # API formatını standart formata çevir
                        return [{'text': item['text'], 'start': item['start'], 'duration': item['duration']}
                               for item in raw_transcript]
                    except:
                        continue

                # Hiçbiri bulunamazsa ilk mevcut transkripti al
                available_transcripts = list(transcript_list)
                if available_transcripts:
                    raw_transcript = available_transcripts[0].fetch()
                    return [{'text': item['text'], 'start': item['start'], 'duration': item['duration']}
                           for item in raw_transcript]
                
            except Exception as e:
                self.logger.warning(f"Transcript API hatası: {e}")
                return self._get_transcript_with_yt_dlp(video_id)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Transkript alma hatası: {e}")
            return None
    
    def _get_transcript_with_yt_dlp(self, video_id: str) -> Optional[List[Dict]]:
        """yt-dlp ile transkript al"""
        try:
            url = f"https://www.youtube.com/watch?v={video_id}"
            
            # Subtitle dosyasını indir
            with tempfile.TemporaryDirectory() as temp_dir:
                cmd = [
                    'yt-dlp',
                    '--write-subs',
                    '--write-auto-subs',
                    '--sub-langs', 'tr,en',
                    '--sub-format', 'vtt',
                    '--skip-download',
                    '--output', f'{temp_dir}/%(title)s.%(ext)s',
                    url
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
                
                if result.returncode == 0:
                    # VTT dosyalarını bul
                    vtt_files = list(Path(temp_dir).glob("*.vtt"))
                    if vtt_files:
                        return self._parse_vtt_file(vtt_files[0])
            
            return None
            
        except Exception as e:
            self.logger.error(f"yt-dlp transkript hatası: {e}")
            return None
    
    def _parse_vtt_file(self, vtt_file: Path) -> List[Dict]:
        """VTT dosyasını parse et"""
        try:
            transcript = []
            
            with open(vtt_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # VTT formatını parse et
            lines = content.split('\n')
            current_entry = {}
            
            for line in lines:
                line = line.strip()
                
                # Zaman damgası satırı
                if '-->' in line:
                    time_parts = line.split(' --> ')
                    if len(time_parts) == 2:
                        start_time = self._parse_time(time_parts[0])
                        current_entry = {
                            'start': start_time,
                            'duration': 3.0,  # Varsayılan süre
                            'text': ''
                        }
                
                # Metin satırı
                elif line and not line.startswith('WEBVTT') and current_entry:
                    # HTML etiketlerini temizle
                    clean_text = re.sub(r'<[^>]+>', '', line)
                    if clean_text.strip():
                        current_entry['text'] = clean_text.strip()
                        transcript.append(current_entry.copy())
                        current_entry = {}
            
            return transcript
            
        except Exception as e:
            self.logger.error(f"VTT parse hatası: {e}")
            return []
    
    def _parse_time(self, time_str: str) -> float:
        """Zaman string'ini saniyeye çevir"""
        try:
            # Format: 00:01:23.456 veya 01:23.456
            time_str = time_str.replace(',', '.')
            parts = time_str.split(':')
            
            if len(parts) == 3:  # HH:MM:SS.mmm
                hours, minutes, seconds = parts
                return float(hours) * 3600 + float(minutes) * 60 + float(seconds)
            elif len(parts) == 2:  # MM:SS.mmm
                minutes, seconds = parts
                return float(minutes) * 60 + float(seconds)
            else:
                return float(parts[0])
                
        except:
            return 0.0
    
    def extract_transcript_segments(self, video_url: str, min_duration: float = 2.0, max_duration: float = 10.0) -> List[Dict]:
        """Video'dan transkript segmentlerini çıkar"""
        try:
            video_id = self.extract_video_id(video_url)
            if not video_id:
                self.logger.error(f"Video ID çıkarılamadı: {video_url}")
                return []
            
            self.logger.info(f"📝 Transkript çıkarılıyor: {video_id}")
            
            # Transkripti al
            transcript = self.get_transcript(video_id)
            if not transcript:
                self.logger.error(f"Transkript bulunamadı: {video_id}")
                return []
            
            self.logger.info(f"✅ {len(transcript)} transkript girişi bulundu")
            
            # Segmentleri işle
            segments = []
            for i, entry in enumerate(transcript):
                text = entry.get('text', '').strip()
                start_time = entry.get('start', 0)
                duration = entry.get('duration', 3.0)
                
                # Metin temizleme
                text = self._clean_text(text)
                
                # Süre kontrolü
                if duration < min_duration or duration > max_duration:
                    continue
                
                # Metin uzunluk kontrolü
                if len(text) < 10 or len(text) > 200:
                    continue
                
                segments.append({
                    'text': text,
                    'start_time': start_time,
                    'duration': duration,
                    'video_id': video_id,
                    'segment_id': i
                })
            
            self.logger.info(f"🎯 {len(segments)} kaliteli segment hazırlandı")
            return segments
            
        except Exception as e:
            self.logger.error(f"Transkript segment çıkarma hatası: {e}")
            return []
    
    def _clean_text(self, text: str) -> str:
        """Metni temizle"""
        try:
            # HTML etiketlerini kaldır
            text = re.sub(r'<[^>]+>', '', text)
            
            # Müzik notasyonlarını kaldır
            text = re.sub(r'\[.*?\]', '', text)
            text = re.sub(r'\(.*?\)', '', text)
            
            # Özel karakterleri temizle
            text = re.sub(r'[^\w\s\.,!?;:-]', '', text)
            
            # Çoklu boşlukları tek yap
            text = re.sub(r'\s+', ' ', text)
            
            return text.strip()
            
        except:
            return text
    
    def save_segments_for_training(self, segments: List[Dict], output_dir: str, speaker_name: str) -> Dict:
        """Segmentleri eğitim için kaydet"""
        try:
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            # Metadata dosyası
            metadata_file = output_path / "transcript_metadata.json"
            
            # Segmentleri kaydet
            training_data = {
                'speaker_name': speaker_name,
                'total_segments': len(segments),
                'segments': segments,
                'created_at': str(Path().cwd()),
                'format': 'youtube_transcript'
            }
            
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(training_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ Transkript metadata kaydedildi: {metadata_file}")
            
            return {
                'success': True,
                'metadata_file': str(metadata_file),
                'total_segments': len(segments),
                'speaker_name': speaker_name
            }
            
        except Exception as e:
            self.logger.error(f"Segment kaydetme hatası: {e}")
            return {'success': False, 'error': str(e)}


def get_transcript_extractor() -> YouTubeTranscriptExtractor:
    """Global transcript extractor instance"""
    return YouTubeTranscriptExtractor()

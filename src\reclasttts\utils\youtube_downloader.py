#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YouTube Video Downloader and Audio Extractor
Fine-tuning için YouTube videolarından ses çıkarma
"""

import os
import re
import subprocess
import tempfile
from typing import List, Dict, Optional, Callable
from pathlib import Path
import librosa
import soundfile as sf

from .logger import get_logger


class YouTubeAudioExtractor:
    """YouTube videolarından ses çıkarma"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        
        # Audio processing settings
        self.target_sample_rate = 22050
        self.target_channels = 1  # Mono
        self.segment_duration = 10  # 10 saniye segmentler
        self.min_segment_duration = 3  # Minimum 3 saniye
        
        # Progress callback
        self.progress_callback: Optional[Callable] = None
        
        self._check_dependencies()
    
    def _check_dependencies(self):
        """Gerekli bağımlılıkları kontrol et"""
        try:
            # yt-dlp kontrolü
            result = subprocess.run(['yt-dlp', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("yt-dlp bulunamadı")
            
            # ffmpeg kontrolü
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("ffmpeg bulunamadı")
                
            self.logger.info("✅ YouTube downloader bağımlılıkları hazır")
            
        except Exception as e:
            self.logger.error(f"❌ Bağımlılık hatası: {e}")
            raise Exception(f"Gerekli araçlar eksik: {e}")
    
    def set_progress_callback(self, callback: Callable):
        """Progress callback ayarla"""
        self.progress_callback = callback
    
    def extract_audio_from_urls(self, 
                               youtube_urls: List[str], 
                               output_dir: str,
                               speaker_name: str = "youtube_speaker") -> Dict:
        """YouTube URL'lerinden ses çıkar"""
        try:
            self.logger.info(f"🎬 {len(youtube_urls)} YouTube videosundan ses çıkarılıyor...")
            
            output_path = Path(output_dir)
            output_path.mkdir(parents=True, exist_ok=True)
            
            all_segments = []
            total_duration = 0
            
            for i, url in enumerate(youtube_urls):
                self.logger.info(f"📹 Video {i+1}/{len(youtube_urls)}: {url}")
                
                if self.progress_callback:
                    self.progress_callback(f"Video {i+1}/{len(youtube_urls)} işleniyor...", 
                                         int((i / len(youtube_urls)) * 80))
                
                try:
                    # Video bilgilerini al
                    video_info = self._get_video_info(url)
                    if not video_info:
                        continue
                    
                    # Ses dosyasını indir
                    audio_file = self._download_audio(url, output_path)
                    if not audio_file:
                        continue
                    
                    # Ses dosyasını işle ve segmentlere böl
                    segments = self._process_audio_file(audio_file, output_path, 
                                                      f"{speaker_name}_{i}")
                    
                    all_segments.extend(segments)
                    
                    # Geçici dosyayı sil
                    if os.path.exists(audio_file):
                        os.remove(audio_file)
                    
                    self.logger.info(f"✅ Video {i+1} işlendi: {len(segments)} segment")
                    
                except Exception as e:
                    self.logger.error(f"❌ Video {i+1} hatası: {e}")
                    continue
            
            # Toplam süreyi hesapla
            total_duration = len(all_segments) * self.segment_duration / 60  # dakika
            
            if self.progress_callback:
                self.progress_callback(f"Tamamlandı! {len(all_segments)} segment oluşturuldu", 100)
            
            self.logger.info(f"🎉 YouTube ses çıkarma tamamlandı: {len(all_segments)} segment, {total_duration:.1f} dakika")
            
            return {
                "success": True,
                "segments": all_segments,
                "total_segments": len(all_segments),
                "total_duration_minutes": total_duration,
                "speaker_name": speaker_name
            }
            
        except Exception as e:
            self.logger.error(f"❌ YouTube ses çıkarma hatası: {e}")
            return {"success": False, "error": str(e)}
    
    def _get_video_info(self, url: str) -> Optional[Dict]:
        """Video bilgilerini al"""
        try:
            cmd = [
                'yt-dlp',
                '--print', '%(title)s',
                '--print', '%(duration)s',
                '--no-download',
                url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) >= 2:
                    return {
                        "title": lines[0],
                        "duration": int(lines[1]) if lines[1].isdigit() else 0
                    }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Video bilgi alma hatası: {e}")
            return None
    
    def _download_audio(self, url: str, output_dir: Path) -> Optional[str]:
        """YouTube'dan ses indir"""
        try:
            # Geçici dosya adı
            temp_file = output_dir / f"temp_audio_{os.getpid()}.wav"
            
            cmd = [
                'yt-dlp',
                '--extract-audio',
                '--audio-format', 'wav',
                '--audio-quality', '0',  # En iyi kalite
                '--output', str(temp_file.with_suffix('.%(ext)s')),
                '--no-playlist',
                url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0 and temp_file.exists():
                return str(temp_file)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Ses indirme hatası: {e}")
            return None
    
    def _process_audio_file(self, audio_file: str, output_dir: Path, prefix: str) -> List[str]:
        """Ses dosyasını işle ve segmentlere böl"""
        try:
            # Ses dosyasını yükle
            audio, sr = librosa.load(audio_file, sr=self.target_sample_rate, mono=True)
            
            # Sessizlikleri tespit et ve temizle
            audio = self._remove_silence(audio, sr)
            
            # Segmentlere böl
            segments = []
            segment_samples = int(self.segment_duration * sr)
            min_samples = int(self.min_segment_duration * sr)
            
            for i in range(0, len(audio), segment_samples):
                segment = audio[i:i + segment_samples]
                
                # Minimum uzunluk kontrolü
                if len(segment) < min_samples:
                    continue
                
                # Segment dosya adı
                segment_filename = f"{prefix}_segment_{len(segments):04d}.wav"
                segment_path = output_dir / segment_filename
                
                # Segment'i kaydet
                sf.write(segment_path, segment, sr)
                segments.append(str(segment_path))
            
            return segments
            
        except Exception as e:
            self.logger.error(f"Ses işleme hatası: {e}")
            return []
    
    def _remove_silence(self, audio, sr, threshold=0.01):
        """Sessizlikleri kaldır"""
        try:
            # Basit sessizlik kaldırma
            # RMS energy hesapla
            frame_length = int(0.025 * sr)  # 25ms frames
            hop_length = int(0.010 * sr)    # 10ms hop
            
            # Energy hesapla
            energy = []
            for i in range(0, len(audio) - frame_length, hop_length):
                frame = audio[i:i + frame_length]
                rms = (frame ** 2).mean() ** 0.5
                energy.append(rms)
            
            # Threshold üzerindeki frame'leri bul
            energy = np.array(energy)
            active_frames = energy > threshold
            
            if not active_frames.any():
                return audio  # Hiç aktif frame yoksa orijinali döndür
            
            # Aktif bölgeleri birleştir
            start_frame = np.where(active_frames)[0][0]
            end_frame = np.where(active_frames)[0][-1]
            
            start_sample = start_frame * hop_length
            end_sample = min((end_frame + 1) * hop_length + frame_length, len(audio))
            
            return audio[start_sample:end_sample]
            
        except Exception:
            return audio  # Hata durumunda orijinali döndür
    
    def validate_youtube_url(self, url: str) -> bool:
        """YouTube URL'ini doğrula"""
        youtube_patterns = [
            r'(?:https?://)?(?:www\.)?youtube\.com/watch\?v=[\w-]+',
            r'(?:https?://)?(?:www\.)?youtu\.be/[\w-]+',
            r'(?:https?://)?(?:www\.)?youtube\.com/playlist\?list=[\w-]+',
        ]
        
        for pattern in youtube_patterns:
            if re.match(pattern, url):
                return True
        
        return False
    
    def extract_playlist_urls(self, playlist_url: str) -> List[str]:
        """Playlist'ten video URL'lerini çıkar"""
        try:
            cmd = [
                'yt-dlp',
                '--flat-playlist',
                '--print', '%(url)s',
                playlist_url
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                urls = [line.strip() for line in result.stdout.strip().split('\n') if line.strip()]
                return urls
            
            return []
            
        except Exception as e:
            self.logger.error(f"Playlist URL çıkarma hatası: {e}")
            return []


# Global instance
_youtube_extractor = None

def get_youtube_extractor() -> YouTubeAudioExtractor:
    """Global YouTube extractor instance'ını al"""
    global _youtube_extractor
    if _youtube_extractor is None:
        _youtube_extractor = YouTubeAudioExtractor()
    return _youtube_extractor

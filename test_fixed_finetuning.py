#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fixed Fine-tuning Test
D<PERSON>zeltilmiş fine-tuning sistemi testi
"""

import os
import time
import json
from pathlib import Path
from src.reclasttts.core.fine_tuner import get_fine_tuner


def test_config_generation():
    """Konfigürasyon oluşturma testi"""
    print("\n🔧 Konfigürasyon Testi")
    print("=" * 50)
    
    fine_tuner = get_fine_tuner()
    
    # Test config oluştur
    config = fine_tuner._create_training_config(
        speaker_name="test_speaker",
        epochs=10,
        batch_size=2,
        lr=1e-5
    )
    
    print("✅ Konfigürasyon oluşturuldu")
    print(f"📁 Config path: {fine_tuner.base_path / 'training_config.json'}")
    
    # Config dosyasını kontrol et
    config_path = fine_tuner.base_path / "training_config.json"
    if config_path.exists():
        with open(config_path, 'r', encoding='utf-8') as f:
            saved_config = json.load(f)
        
        print(f"📊 Model: {saved_config.get('model', 'N/A')}")
        print(f"📊 Batch Size: {saved_config.get('batch_size', 'N/A')}")
        print(f"📊 Epochs: {saved_config.get('epochs', 'N/A')}")
        print(f"📊 Learning Rate: {saved_config.get('lr', 'N/A')}")
        print(f"📊 Dataset Path: {saved_config.get('datasets', [{}])[0].get('path', 'N/A')}")
        
        # Gerekli alanları kontrol et
        required_fields = ['model', 'datasets', 'batch_size', 'epochs', 'lr']
        missing_fields = [field for field in required_fields if field not in saved_config]
        
        if missing_fields:
            print(f"❌ Eksik alanlar: {missing_fields}")
            return False
        else:
            print("✅ Tüm gerekli alanlar mevcut")
            return True
    else:
        print("❌ Config dosyası oluşturulamadı")
        return False


def test_dataset_structure():
    """Veri seti yapısını kontrol et"""
    print("\n📊 Veri Seti Yapısı Kontrolü")
    print("=" * 50)
    
    fine_tuner = get_fine_tuner()
    dataset_path = fine_tuner.dataset_path
    
    if not dataset_path.exists():
        print("❌ Dataset klasörü bulunamadı")
        return False
    
    # Gerekli dosyaları kontrol et
    required_files = [
        "metadata.csv",
        "speakers.json"
    ]
    
    wavs_path = dataset_path / "wavs"
    
    print(f"📁 Dataset path: {dataset_path}")
    print(f"📁 Wavs path: {wavs_path}")
    
    for file in required_files:
        file_path = dataset_path / file
        if file_path.exists():
            print(f"✅ {file} mevcut")
            
            if file == "metadata.csv":
                # Metadata formatını kontrol et
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                print(f"📊 Metadata satır sayısı: {len(lines)}")
                if lines:
                    print(f"📝 İlk satır örneği: {lines[0].strip()}")
                    
                    # Format kontrolü: audio_file|text
                    parts = lines[0].strip().split('|')
                    if len(parts) == 2:
                        print("✅ Metadata formatı doğru (audio_file|text)")
                    else:
                        print(f"❌ Metadata formatı yanlış: {len(parts)} alan (2 olmalı)")
                        return False
        else:
            print(f"❌ {file} eksik")
            return False
    
    # Wav dosyalarını kontrol et
    if wavs_path.exists():
        wav_files = list(wavs_path.glob("*.wav"))
        print(f"🎵 Wav dosya sayısı: {len(wav_files)}")
        
        if len(wav_files) > 0:
            print(f"📝 İlk wav dosyası: {wav_files[0].name}")
            print("✅ Wav dosyaları mevcut")
            return True
        else:
            print("❌ Wav dosyası bulunamadı")
            return False
    else:
        print("❌ Wavs klasörü bulunamadı")
        return False


def test_training_command():
    """Training komutunu test et"""
    print("\n🚀 Training Komutu Testi")
    print("=" * 50)
    
    fine_tuner = get_fine_tuner()
    
    # Test config oluştur
    config = fine_tuner._create_training_config(
        speaker_name="test_speaker",
        epochs=1,  # Kısa test için
        batch_size=1,
        lr=1e-5
    )
    
    # Training komutunu oluştur (çalıştırmadan)
    cmd = [
        "python", "-m", "TTS.bin.train_tts",
        "--config_path", str(fine_tuner.base_path / "training_config.json"),
        "--coqpit.restore_path", "tts_models/multilingual/multi-dataset/xtts_v2"
    ]
    
    print("🔧 Training komutu:")
    print(" ".join(cmd))
    
    # Config dosyasının varlığını kontrol et
    config_path = fine_tuner.base_path / "training_config.json"
    if config_path.exists():
        print("✅ Config dosyası mevcut")
        
        # TTS modülünün varlığını kontrol et
        try:
            import TTS.bin.train_tts
            print("✅ TTS.bin.train_tts modülü mevcut")
            return True
        except ImportError as e:
            print(f"❌ TTS modülü import hatası: {e}")
            return False
    else:
        print("❌ Config dosyası bulunamadı")
        return False


def test_fine_tuner_status():
    """Fine-tuner durumunu kontrol et"""
    print("\n📊 Fine-tuner Durum Kontrolü")
    print("=" * 50)
    
    fine_tuner = get_fine_tuner()
    status = fine_tuner.get_status()
    
    print(f"🔄 Training aktif: {status['is_training']}")
    print(f"📈 Progress: {status['progress']}%")
    print(f"📊 Status: {status['status']}")
    print(f"📝 Log satırları: {status['log_lines']}")
    print(f"📊 Dataset hazır: {status['dataset_ready']}")
    print(f"🤖 Model hazır: {status['model_ready']}")
    
    return True


def main():
    """Ana test fonksiyonu"""
    print("🧪 Fixed Fine-tuning System Test")
    print("=" * 60)
    print("🎯 Düzeltilmiş fine-tuning sistemi test ediliyor...")
    
    tests = [
        ("Konfigürasyon Testi", test_config_generation),
        ("Veri Seti Yapısı", test_dataset_structure),
        ("Training Komutu", test_training_command),
        ("Fine-tuner Durumu", test_fine_tuner_status)
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 {test_name} başlatılıyor...")
        try:
            if test_func():
                print(f"✅ {test_name} başarılı")
                passed_tests += 1
            else:
                print(f"❌ {test_name} başarısız")
        except Exception as e:
            print(f"❌ {test_name} hata: {e}")
    
    print(f"\n📊 Test Sonuçları: {passed_tests}/{total_tests} başarılı")
    
    if passed_tests == total_tests:
        print("🎉 Tüm testler başarılı! Fine-tuning sistemi hazır.")
        print("\n🚀 Sonraki adımlar:")
        print("1. Web UI'de Fine-tuning tab'ına git")
        print("2. Eğitimi yeniden başlat")
        print("3. Düzeltilmiş konfigürasyon kullanılacak")
    else:
        print("⚠️ Bazı testler başarısız. Sorunları giderin.")
    
    return passed_tests == total_tests


if __name__ == "__main__":
    main()
